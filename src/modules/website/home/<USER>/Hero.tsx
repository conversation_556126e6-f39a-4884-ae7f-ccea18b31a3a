import { conferenceInfo, conferenceDate } from "../data";
import CountdownCard from "./CountdownCard";

export default function Hero() {
  return (
    <section className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto px-6 py-16">
        {/* Main Hero Container - Left Right Layout */}
        <div className="flex flex-col lg:flex-row items-center justify-between min-h-[80vh] gap-12">
          {/* Left Side - CTA Content */}
          <div className="flex flex-col space-y-8 lg:w-1/2">
            {/* Badge */}
            <div className="flex">
              <span className="bg-secondary text-secondary-foreground font-myriad font-semibold px-4 py-2 rounded-full text-sm">
                🇰🇪 IKIA Conference 2025
              </span>
            </div>

            {/* Main Title */}
            <div className="flex flex-col space-y-3">
              <h1 className="font-acquire font-bold text-2xl lg:text-3xl text-primary leading-tight">
                {conferenceInfo.title}
              </h1>
              <h2 className="font-acquire font-bold text-xl lg:text-2xl text-secondary">
                {conferenceInfo.subtitle}
              </h2>
              <p className="font-acquire text-base lg:text-lg text-muted-foreground">
                {conferenceInfo.description}
              </p>
            </div>

            {/* Countdown Card */}
            <div className="flex justify-center lg:justify-start">
              <CountdownCard
                targetDate={conferenceDate}
                className="max-w-md w-full"
              />
            </div>
          </div>

          {/* Right Side - Image Placeholder */}
          <div className="flex lg:w-1/2 justify-center lg:justify-end">
            <div className="w-full max-w-lg aspect-square bg-gradient-to-br from-secondary/10 to-primary/10 rounded-2xl border-2 border-dashed border-muted-foreground/30 flex flex-col items-center justify-center space-y-4">
              <div className="w-24 h-24 bg-secondary/20 rounded-full flex items-center justify-center">
                <span className="text-4xl">🌿</span>
              </div>
              <div className="text-center space-y-2">
                <p className="font-acquire font-bold text-lg text-primary">
                  Conference Visual
                </p>
                <p className="font-myriad text-sm text-muted-foreground">
                  Image placeholder
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
