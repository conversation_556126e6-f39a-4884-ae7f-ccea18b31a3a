import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import {
  Users,
  TrendingUp,
  Globe,
  Lightbulb,
  Target,
  CheckCircle,
  ArrowRight
} from 'lucide-react';

export default function FeaturesSection() {
  const features = [
    {
      icon: Users,
      title: "Community Engagement",
      description: "Connect with indigenous knowledge holders and community leaders"
    },
    {
      icon: TrendingUp,
      title: "Investment Opportunities", 
      description: "Discover viable investment prospects in natural products"
    },
    {
      icon: Globe,
      title: "Global Partnerships",
      description: "Build international collaborations and strategic alliances"
    },
    {
      icon: Lightbulb,
      title: "Innovation Showcase",
      description: "Explore cutting-edge applications of traditional knowledge"
    }
  ];

  return (
    <section className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6">
        {/* Main Container - Left Right Layout */}
        <div className="flex flex-col lg:flex-row items-center justify-between gap-16">
          
          {/* Left Side - Features Grid */}
          <div className="lg:w-1/2">
            <div className="grid grid-cols-2 gap-6">
              {features.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <Card 
                    key={index}
                    className="border-2 border-muted hover:border-secondary/40 transition-all duration-300 hover:shadow-lg group"
                  >
                    <CardContent className="p-6 text-center space-y-4">
                      <div className="flex justify-center">
                        <div className="w-12 h-12 bg-secondary/10 rounded-lg flex items-center justify-center group-hover:bg-secondary/20 transition-colors">
                          <IconComponent className="w-6 h-6 text-secondary" />
                        </div>
                      </div>
                      <h3 className="font-acquire font-bold text-lg text-primary">
                        {feature.title}
                      </h3>
                      <p className="font-myriad text-sm text-muted-foreground leading-relaxed">
                        {feature.description}
                      </p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Right Side - Content Block */}
          <div className="lg:w-1/2 space-y-8">
            
            {/* Main Content */}
            <div className="space-y-6">
              <h2 className="font-acquire font-bold text-3xl lg:text-4xl text-primary leading-tight">
                IKIA Investment Conference
              </h2>
              
              <div className="space-y-4">
                <p className="font-myriad text-lg text-muted-foreground leading-relaxed">
                  Flagship program under <strong>Kenya Vision 2030</strong>
                </p>
                
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-secondary mt-0.5 flex-shrink-0" />
                    <p className="font-myriad text-base text-muted-foreground">
                      Employment and wealth creation opportunities
                    </p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-secondary mt-0.5 flex-shrink-0" />
                    <p className="font-myriad text-base text-muted-foreground">
                      Poverty alleviation through indigenous knowledge
                    </p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-secondary mt-0.5 flex-shrink-0" />
                    <p className="font-myriad text-base text-muted-foreground">
                      Improved biodiversity management practices
                    </p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-secondary mt-0.5 flex-shrink-0" />
                    <p className="font-myriad text-base text-muted-foreground">
                      Achievement of double-digit GDP growth
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Statistics */}
            <div className="bg-muted/30 rounded-lg p-6 border border-muted">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="font-acquire font-bold text-2xl text-primary">13</div>
                  <div className="font-myriad text-xs text-muted-foreground uppercase tracking-wide">Counties</div>
                </div>
                <div>
                  <div className="font-acquire font-bold text-2xl text-primary">100+</div>
                  <div className="font-myriad text-xs text-muted-foreground uppercase tracking-wide">IKIAs</div>
                </div>
                <div>
                  <div className="font-acquire font-bold text-2xl text-secondary">✓</div>
                  <div className="font-myriad text-xs text-muted-foreground uppercase tracking-wide">Ready</div>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link href="/registration">
                <Button
                  size="lg"
                  className="w-full sm:w-auto px-8 py-4 text-lg font-semibold bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]"
                >
                  Join the Conference
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>

              <Link href="/about">
                <Button
                  variant="outline"
                  size="lg"
                  className="w-full sm:w-auto px-8 py-4 text-lg font-semibold border-2 border-secondary text-secondary hover:bg-secondary hover:text-secondary-foreground transition-all duration-300"
                >
                  Learn More
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
