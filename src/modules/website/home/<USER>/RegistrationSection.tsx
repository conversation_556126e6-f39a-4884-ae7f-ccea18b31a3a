import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { 
  Users, 
  Store, 
  TrendingUp, 
  Crown, 
  Building2,
  ArrowRight,
  Calendar,
  MapPin,
  Globe
} from 'lucide-react';

export default function RegistrationSection() {
  const registrationTypes = [
    {
      id: "guest",
      title: "Conference Delegate",
      subtitle: "Join the Knowledge Exchange",
      description: "Participate in sessions, workshops, and networking opportunities.",
      icon: Users,
      color: "from-green-600 to-green-700",
      price: "From KES 15,000",
      popular: true,
      features: [
        "Access to all sessions",
        "Networking events",
        "Conference materials",
        "Certificate of participation"
      ]
    },
    {
      id: "exhibitor",
      title: "Trade Fair Exhibitor",
      subtitle: "Showcase Your Innovation",
      description: "Display your Indigenous Knowledge-based products and services.",
      icon: Store,
      color: "from-amber-700 to-amber-800",
      price: "From KES 50,000",
      popular: false,
      features: [
        "3x3m exhibition booth",
        "2 delegate passes",
        "Marketing opportunities",
        "Networking access"
      ]
    },
    {
      id: "investor",
      title: "Investment Discovery",
      subtitle: "Explore Opportunities",
      description: "Connect with innovative Indigenous Knowledge enterprises.",
      icon: TrendingUp,
      color: "from-yellow-600 to-orange-600",
      price: "From KES 25,000",
      popular: false,
      features: [
        "Investment showcases",
        "Due diligence support",
        "Networking with entrepreneurs",
        "Market intelligence"
      ]
    },
    {
      id: "vip",
      title: "VIP Delegate",
      subtitle: "Premium Experience",
      description: "Exclusive access for dignitaries and distinguished guests.",
      icon: Crown,
      color: "from-blue-600 to-blue-700",
      price: "By Invitation",
      popular: false,
      features: [
        "VIP dealrooms",
        "Gala dinner access",
        "Priority registration",
        "Premium amenities"
      ]
    }
  ];

  const conferenceHighlights = [
    { icon: Globe, label: "Countries", value: "13+" },
    { icon: Users, label: "Expected Attendees", value: "500+" },
    { icon: TrendingUp, label: "IKIA Projects", value: "100+" },
    { icon: Building2, label: "Innovation Categories", value: "6" },
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-background via-muted/20 to-background">
      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-secondary/10 text-secondary px-4 py-2 rounded-full text-sm font-semibold mb-6">
            <Calendar className="w-4 h-4" />
            <span>November 18-21, 2025</span>
          </div>
          
          <h2 className="font-acquire font-bold text-3xl lg:text-4xl text-primary mb-4">
            Register for IKIA Conference 2025
          </h2>
          
          <p className="font-myriad text-lg text-muted-foreground max-w-3xl mx-auto mb-6">
            Join the 1st International Investment Conference and Trade Fair on Indigenous Knowledge Intellectual Assets
          </p>

          <div className="flex items-center justify-center space-x-2 text-muted-foreground mb-8">
            <MapPin className="w-5 h-5 text-secondary" />
            <span className="font-myriad">Thika Greens Golf Resort, Murang'a, Kenya</span>
          </div>

          {/* Conference Highlights */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            {conferenceHighlights.map((highlight, index) => {
              const IconComponent = highlight.icon;
              return (
                <div key={index} className="text-center">
                  <div className="w-12 h-12 bg-secondary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <IconComponent className="w-6 h-6 text-secondary" />
                  </div>
                  <div className="font-acquire font-bold text-xl text-primary">{highlight.value}</div>
                  <div className="font-myriad text-sm text-muted-foreground">{highlight.label}</div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Registration Types Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {registrationTypes.map((type, index) => {
            const IconComponent = type.icon;
            return (
              <Card 
                key={type.id}
                className={`
                  relative overflow-hidden border-2 hover:border-secondary/40 transition-all duration-300 hover:shadow-xl group
                  ${type.popular ? 'border-secondary ring-2 ring-secondary/20' : 'border-muted'}
                `}
              >
                {type.popular && (
                  <div className="absolute top-4 right-4 bg-secondary text-secondary-foreground text-xs font-bold px-2 py-1 rounded-full">
                    Popular
                  </div>
                )}
                
                <CardContent className="p-6 space-y-4">
                  <div className={`w-12 h-12 bg-gradient-to-br ${type.color} rounded-lg flex items-center justify-center mb-4`}>
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  
                  <div>
                    <h3 className="font-acquire font-bold text-lg text-primary mb-1">
                      {type.title}
                    </h3>
                    <p className="font-myriad text-sm text-secondary font-semibold mb-2">
                      {type.subtitle}
                    </p>
                    <p className="font-myriad text-sm text-muted-foreground leading-relaxed mb-4">
                      {type.description}
                    </p>
                  </div>

                  <div className="space-y-2">
                    {type.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-2">
                        <div className="w-1.5 h-1.5 bg-secondary rounded-full"></div>
                        <span className="font-myriad text-xs text-muted-foreground">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <div className="pt-4 border-t border-muted">
                    <div className="font-acquire font-bold text-lg text-primary mb-3">
                      {type.price}
                    </div>
                    
                    <Link href={`/registration`}>
                      <Button 
                        className="w-full group-hover:bg-primary/90 transition-colors"
                        size="sm"
                      >
                        Select Plan
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Main CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-2xl p-8 border border-muted">
            <h3 className="font-acquire font-bold text-2xl text-primary mb-4">
              Ready to Join the Conference?
            </h3>
            <p className="font-myriad text-muted-foreground mb-6 max-w-2xl mx-auto">
              Choose your registration type and be part of this groundbreaking event that bridges ancestral wisdom with future innovation.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/registration">
                <Button 
                  size="lg"
                  className="px-12 py-4 text-lg font-semibold bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]"
                >
                  Start Registration
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              
              <Link href="/about">
                <Button 
                  variant="outline"
                  size="lg"
                  className="px-12 py-4 text-lg font-semibold border-2 border-secondary text-secondary hover:bg-secondary hover:text-secondary-foreground transition-all duration-300"
                >
                  View Program
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
