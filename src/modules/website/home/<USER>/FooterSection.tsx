import { Card, CardContent } from '@/components/ui/card';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Globe, 
  Calendar,
  Users,
  ExternalLink
} from 'lucide-react';

export default function FooterSection() {
  const contactInfo = [
    {
      icon: Mail,
      label: "Email",
      value: "<EMAIL>",
      link: "mailto:<EMAIL>"
    },
    {
      icon: Phone,
      label: "Phone",
      value: "+254 700 123 456",
      link: "tel:+254700123456"
    },
    {
      icon: MapPin,
      label: "Venue",
      value: "Kenyatta International Conference Centre, Nairobi",
      link: "https://maps.google.com"
    },
    {
      icon: Globe,
      label: "Website",
      value: "www.ikiaconference.co.ke",
      link: "https://www.ikiaconference.co.ke"
    }
  ];

  const quickLinks = [
    { label: "About IKIA", href: "/about" },
    { label: "Speakers", href: "/speakers" },
    { label: "Agenda", href: "/agenda" },
    { label: "Registration", href: "/register" },
    { label: "Contact", href: "/contact" },
    { label: "Accommodation", href: "/accommodation" }
  ];

  const partners = [
    "Ministry of Trade & Industry",
    "Kenya Investment Authority", 
    "Export Promotion Council",
    "Kenya Wildlife Service",
    "Traditional Medicine Board"
  ];

  return (
    <footer className="bg-primary text-primary-foreground">
      {/* Main Footer Content */}
      <div className="py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            
            {/* Conference Info */}
            <div className="lg:col-span-2 space-y-6">
              <div className="space-y-4">
                <h3 className="font-acquire font-bold text-2xl">
                  🇰🇪 IKIA Conference 2025
                </h3>
                <p className="font-myriad text-lg opacity-90">
                  First International Investment Conference & Trade Fair on Kenya's Indigenous Knowledge Intellectual Assets
                </p>
              </div>

              {/* Conference Details */}
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Calendar className="w-5 h-5 text-secondary" />
                  <span className="font-myriad">Monday, September 15, 2025</span>
                </div>
                <div className="flex items-center space-x-3">
                  <MapPin className="w-5 h-5 text-secondary" />
                  <span className="font-myriad">Kenyatta International Conference Centre, Nairobi</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Users className="w-5 h-5 text-secondary" />
                  <span className="font-myriad">Expected 500+ Participants</span>
                </div>
              </div>

              {/* Final CTA */}
              <div className="pt-4">
                <button className="bg-secondary text-secondary-foreground font-myriad font-bold px-8 py-4 rounded-sm hover:bg-secondary/90 hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2 focus:ring-offset-primary">
                  Register Now - Early Bird Pricing
                </button>
              </div>
            </div>

            {/* Quick Links */}
            <div className="space-y-6">
              <h4 className="font-acquire font-bold text-lg">Quick Links</h4>
              <ul className="space-y-3">
                {quickLinks.map((link, index) => (
                  <li key={index}>
                    <a 
                      href={link.href}
                      className="font-myriad text-sm opacity-90 hover:opacity-100 hover:text-secondary transition-colors duration-200 flex items-center space-x-2"
                    >
                      <ExternalLink className="w-3 h-3" />
                      <span>{link.label}</span>
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Information */}
            <div className="space-y-6">
              <h4 className="font-acquire font-bold text-lg">Contact Information</h4>
              <div className="space-y-4">
                {contactInfo.map((contact, index) => {
                  const IconComponent = contact.icon;
                  return (
                    <a
                      key={index}
                      href={contact.link}
                      className="flex items-start space-x-3 opacity-90 hover:opacity-100 hover:text-secondary transition-colors duration-200 group"
                    >
                      <IconComponent className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="font-myriad text-xs uppercase tracking-wide opacity-75">
                          {contact.label}
                        </div>
                        <div className="font-myriad text-sm">
                          {contact.value}
                        </div>
                      </div>
                    </a>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Partners Section */}
          <div className="mt-12 pt-8 border-t border-primary-foreground/20">
            <div className="text-center space-y-4">
              <h4 className="font-acquire font-bold text-lg">Strategic Partners</h4>
              <div className="flex flex-wrap justify-center gap-4">
                {partners.map((partner, index) => (
                  <span 
                    key={index}
                    className="font-myriad text-xs bg-primary-foreground/10 px-3 py-1 rounded-full border border-primary-foreground/20"
                  >
                    {partner}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="bg-primary-foreground/10 py-6">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="font-myriad text-sm opacity-75">
              © 2025 IKIA Conference. All rights reserved. | Flagship program under Kenya Vision 2030
            </div>
            <div className="flex space-x-6">
              <a href="/privacy" className="font-myriad text-sm opacity-75 hover:opacity-100 transition-opacity">
                Privacy Policy
              </a>
              <a href="/terms" className="font-myriad text-sm opacity-75 hover:opacity-100 transition-opacity">
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
