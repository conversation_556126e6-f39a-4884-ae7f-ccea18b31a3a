@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Font Declarations */
@font-face {
  font-family: 'ACQUIRE';
  src: url('/assets/fonts/AquireLight-YzE0o.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'ACQUIRE';
  src: url('/assets/fonts/Aquire-BW0ox.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'ACQUIRE';
  src: url('/assets/fonts/AquireBold-8Ma60.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Myriad Pro';
  src: url('/assets/fonts/MyriadPro-Light.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Myriad Pro';
  src: url('/assets/fonts/MYRIADPRO-REGULAR.OTF') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Myriad Pro';
  src: url('/assets/fonts/MYRIADPRO-SEMIBOLD.OTF') format('opentype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Myriad Pro';
  src: url('/assets/fonts/MYRIADPRO-BOLD.OTF') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* CSS Variables for Tailwind v3 */
:root {
  /* Base colors - Light mode */
  --background: 0 0% 100%; /* hsl format for Tailwind */
  --foreground: 224 71.4% 4.1%;

  /* IKIA Semantic Color Mapping */
  --primary: 14 76% 17%; /* IKIA Deep Brown - #7E2518 */
  --primary-foreground: 0 0% 98%;
  --secondary: 142 74% 27%; /* IKIA Green - #159147 */
  --secondary-foreground: 0 0% 98%;
  --accent: 42 77% 55%; /* IKIA Yellow Ochre - #E8B32C */
  --accent-foreground: 224 71.4% 4.1%;
  --muted: 220 14.3% 95.9%;
  --muted-foreground: 220 8.9% 46.1%;

  /* Status colors using IKIA palette */
  --destructive: 21 62% 50%; /* IKIA Sienna - #C85E36 */
  --destructive-foreground: 0 0% 98%;
  --warning: 42 77% 55%; /* IKIA Yellow Ochre */
  --warning-foreground: 224 71.4% 4.1%;
  --success: 142 74% 27%; /* IKIA Green */
  --success-foreground: 0 0% 98%;
  --info: 204 50% 68%; /* IKIA Blue - #81B1D8 */
  --info-foreground: 0 0% 98%;

  /* UI elements */
  --card: 0 0% 100%;
  --card-foreground: 224 71.4% 4.1%;
  --popover: 0 0% 100%;
  --popover-foreground: 224 71.4% 4.1%;
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --ring: 14 76% 17%; /* IKIA Deep Brown for focus rings */

  /* Sidebar using IKIA colors */
  --sidebar-background: 0 0% 98%;
  --sidebar-foreground: 224 71.4% 4.1%;
  --sidebar-primary: 14 76% 17%; /* IKIA Deep Brown */
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 220 14.3% 95.9%;
  --sidebar-accent-foreground: 14 76% 17%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 14 76% 17%;
}

/* Dark mode variables */
[data-theme='dark'] {
  /* Base colors for dark mode */
  --background: 14 50% 8%; /* Dark brown background */
  --foreground: 0 0% 98%;

  /* IKIA Semantic Colors for Dark Mode */
  --primary: 14 25% 75%; /* Lighter brown for dark mode */
  --primary-foreground: 14 50% 8%;
  --secondary: 142 45% 65%; /* Lighter green for dark mode */
  --secondary-foreground: 14 50% 8%;
  --accent: 42 55% 70%; /* Lighter yellow for dark mode */
  --accent-foreground: 14 50% 8%;
  --muted: 14 50% 20%;
  --muted-foreground: 0 0% 70%;

  /* Status colors for dark mode */
  --destructive: 21 45% 65%; /* Lighter sienna for dark mode */
  --destructive-foreground: 0 0% 98%;
  --warning: 42 55% 70%; /* Lighter yellow for dark mode */
  --warning-foreground: 14 50% 8%;
  --success: 142 45% 65%; /* Lighter green for dark mode */
  --success-foreground: 14 50% 8%;
  --info: 204 35% 70%; /* Lighter blue for dark mode */
  --info-foreground: 14 50% 8%;

  /* UI elements for dark mode */
  --card: 14 50% 15%;
  --card-foreground: 0 0% 98%;
  --popover: 14 50% 15%;
  --popover-foreground: 0 0% 98%;
  --border: 14 50% 20%;
  --input: 14 50% 20%;
  --ring: 14 25% 75%; /* Light brown for focus rings */

  /* Sidebar for dark mode */
  --sidebar-background: 14 50% 15%;
  --sidebar-foreground: 0 0% 98%;
  --sidebar-primary: 14 25% 75%; /* Light brown */
  --sidebar-primary-foreground: 14 50% 8%;
  --sidebar-accent: 14 50% 20%;
  --sidebar-accent-foreground: 0 0% 98%;
  --sidebar-border: 14 50% 20%;
  --sidebar-ring: 14 25% 75%;
}

/* Fallback for .dark class (common in many setups) */
.dark {
  /* Base colors for dark mode */
  --background: 14 50% 8%; /* Dark brown background */
  --foreground: 0 0% 98%;

  /* IKIA Semantic Colors for Dark Mode */
  --primary: 14 25% 75%; /* Lighter brown for dark mode */
  --primary-foreground: 14 50% 8%;
  --secondary: 142 45% 65%; /* Lighter green for dark mode */
  --secondary-foreground: 14 50% 8%;
  --accent: 42 55% 70%; /* Lighter yellow for dark mode */
  --accent-foreground: 14 50% 8%;
  --muted: 14 50% 20%;
  --muted-foreground: 0 0% 70%;

  /* Status colors for dark mode */
  --destructive: 21 45% 65%; /* Lighter sienna for dark mode */
  --destructive-foreground: 0 0% 98%;
  --warning: 42 55% 70%; /* Lighter yellow for dark mode */
  --warning-foreground: 14 50% 8%;
  --success: 142 45% 65%; /* Lighter green for dark mode */
  --success-foreground: 14 50% 8%;
  --info: 204 35% 70%; /* Lighter blue for dark mode */
  --info-foreground: 14 50% 8%;

  /* UI elements for dark mode */
  --card: 14 50% 15%;
  --card-foreground: 0 0% 98%;
  --popover: 14 50% 15%;
  --popover-foreground: 0 0% 98%;
  --border: 14 50% 20%;
  --input: 14 50% 20%;
  --ring: 14 25% 75%; /* Light brown for focus rings */

  /* Sidebar for dark mode */
  --sidebar-background: 14 50% 15%;
  --sidebar-foreground: 0 0% 98%;
  --sidebar-primary: 14 25% 75%; /* Light brown */
  --sidebar-primary-foreground: 14 50% 8%;
  --sidebar-accent: 14 50% 20%;
  --sidebar-accent-foreground: 0 0% 98%;
  --sidebar-border: 14 50% 20%;
  --sidebar-ring: 14 25% 75%;
}

@layer base {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: unset;
    font-weight: unset;
  }

  * {
    border-color: theme('colors.border');
  }

  body {
    @apply bg-background text-foreground min-h-screen flex flex-col;
  }
}

@layer utilities {
  .animate-blob {
    animation: blob 7s infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  .animation-delay-6000 {
    animation-delay: 6s;
  }

  .animation-delay-8000 {
    animation-delay: 8s;
  }

  .animation-delay-300 {
    animation-delay: 300ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }

  .animate-fade-in-up {
    animation: fadeInUp 1s ease-out forwards;
    opacity: 0;
    transform: translateY(40px);
  }

  .animate-spin-slow {
    animation: spin 20s linear infinite;
  }

  .animate-bounce-slow {
    animation: bounce 3s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s ease-in-out infinite;
  }

  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #8b5cf6, #ec4899);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #7c3aed, #db2777);
}

html {
  opacity: 0;
}

html[data-theme='dark'],
html[data-theme='light'] {
  opacity: initial;
}

/* Hero Section Responsive Curves */
.hero-curve-main {
  clip-path: polygon(0 0, 100% 0, 100% 90%, 0 100%);
}

.hero-curve-secondary {
  clip-path: polygon(0 0, 100% 0, 100% 95%, 0 100%);
}

@media (min-width: 768px) {
  .hero-curve-main {
    clip-path: polygon(0 0, 100% 0, 85% 100%, 0 85%);
  }

  .hero-curve-secondary {
    clip-path: polygon(0 0, 100% 0, 90% 100%, 0 90%);
  }
}
