"use client"

import type React from "react"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { ArrowLeft, Users, Upload, Camera } from "lucide-react"

const thematicAreas = [
  "Traditional Foods & Nutrition",
  "Local Remedies & Traditional Medicine",
  "Musicology & Cultural Arts",
  "Cultural Tourism & Heritage",
  "Indigenous Technologies & Innovations",
  "Sui Generis Intellectual Property Systems",
]

const countries = [
  "Kenya",
  "Uganda",
  "Tanzania",
  "Rwanda",
  "Burundi",
  "South Sudan",
  "Ethiopia",
  "Somalia",
  "United States",
  "United Kingdom",
  "Other",
]

const accessibilityOptions = [
  "Wheelchair accessibility",
  "Sign language interpretation",
  "Large print materials",
  "Audio assistance",
  "Dietary accommodations",
  "Other accessibility needs",
]

const packageOptions = [
  {
    id: "basic",
    name: "Basic Package",
    price: "KES 15,000",
    description: "Conference access only",
    features: [
      "Access to all conference sessions",
      "Conference materials",
      "Tea breaks",
      "Certificate of participation",
    ],
  },
  {
    id: "standard",
    name: "Standard Package",
    price: "KES 25,000",
    description: "Conference + networking",
    features: [
      "All Basic Package features",
      "Networking sessions",
      "Welcome dinner",
      "Gift pack",
    ],
    popular: true,
  },
  {
    id: "premium",
    name: "Premium Package",
    price: "KES 35,000",
    description: "Full experience",
    features: [
      "All Standard Package features",
      "Site visits",
      "Premium gift pack",
      "Priority seating",
      "Lunch for all days",
    ],
  },
]

export default function GuestRegistrationForm() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    organization: "",
    position: "",
    country: "",
    city: "",
    
    // Professional Information
    thematicAreas: [] as string[],
    experience: "",
    interests: "",
    
    // Package Selection
    selectedPackage: "",
    
    // Accessibility
    accessibilityNeeds: [] as string[],
    dietaryRequirements: "",
    
    // Additional Information
    hearAbout: "",
    expectations: "",
    networking: "",
    
    // Photo Upload
    profilePhoto: null as File | null,
    
    // Agreements
    termsAccepted: false,
    marketingConsent: false,
  })

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleThematicAreaChange = (area: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      thematicAreas: checked 
        ? [...prev.thematicAreas, area]
        : prev.thematicAreas.filter(a => a !== area)
    }))
  }

  const handleAccessibilityChange = (option: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      accessibilityNeeds: checked 
        ? [...prev.accessibilityNeeds, option]
        : prev.accessibilityNeeds.filter(a => a !== option)
    }))
  }

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setFormData(prev => ({
        ...prev,
        profilePhoto: file
      }))
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Here you would typically send the data to your backend
    console.log("Form submitted:", formData)
    router.push("/registration/review")
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Registration Types
        </Button>
        
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-green-600 to-green-700 rounded-2xl flex items-center justify-center">
            <Users className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Conference Delegate Registration
            </h1>
            <p className="text-green-600 font-semibold">Join the Knowledge Exchange</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange("firstName", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange("lastName", e.target.value)}
                  required
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="organization">Organization/Institution *</Label>
                <Input
                  id="organization"
                  value={formData.organization}
                  onChange={(e) => handleInputChange("organization", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="position">Position/Title *</Label>
                <Input
                  id="position"
                  value={formData.position}
                  onChange={(e) => handleInputChange("position", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="country">Country *</Label>
                <Select value={formData.country} onValueChange={(value) => handleInputChange("country", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  required
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Professional Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Professional Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label className="text-base font-semibold mb-4 block">
                Areas of Interest (Select all that apply) *
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {thematicAreas.map((area) => (
                  <div key={area} className="flex items-center space-x-2">
                    <Checkbox
                      id={area}
                      checked={formData.thematicAreas.includes(area)}
                      onCheckedChange={(checked) => handleThematicAreaChange(area, checked as boolean)}
                    />
                    <Label htmlFor={area} className="text-sm">
                      {area}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="experience">Professional Experience (Years) *</Label>
              <Select value={formData.experience} onValueChange={(value) => handleInputChange("experience", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select your experience level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0-2">0-2 years</SelectItem>
                  <SelectItem value="3-5">3-5 years</SelectItem>
                  <SelectItem value="6-10">6-10 years</SelectItem>
                  <SelectItem value="11-15">11-15 years</SelectItem>
                  <SelectItem value="16+">16+ years</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="interests">Specific Interests & Goals</Label>
              <Textarea
                id="interests"
                placeholder="Tell us about your specific interests and what you hope to achieve at the conference..."
                value={formData.interests}
                onChange={(e) => handleInputChange("interests", e.target.value)}
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        {/* Continue Button */}
        <div className="flex justify-center">
          <Button
            type="submit"
            className="px-12 py-3 text-lg font-semibold"
            style={{ fontFamily: "Myriad Pro, Arial, sans-serif" }}
          >
            Continue to Package Selection
          </Button>
        </div>
      </form>
    </div>
  )
}
