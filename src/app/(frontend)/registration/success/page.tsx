"use client"

import { useRouter } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { 
  CheckCircle, 
  Download, 
  Calendar, 
  MapPin, 
  Mail, 
  Phone,
  Home,
  FileText,
  Share2
} from "lucide-react"

export default function RegistrationSuccess() {
  const router = useRouter()

  // Mock registration data - in real app, this would come from the completed registration
  const registrationDetails = {
    confirmationNumber: "IKIA2025-CDG-001234",
    registrationType: "Conference Delegate",
    package: "Standard Package",
    amount: "KES 25,000",
    paymentMethod: "M-Pesa",
    registrantName: "<PERSON> Doe",
    email: "<EMAIL>",
    registrationDate: new Date().toLocaleDateString(),
  }

  const conferenceDetails = {
    name: "1st International Investment Conference and Trade Fair on Indigenous Knowledge Intellectual Assets",
    dates: "November 18-21, 2025",
    venue: "Thika Greens Golf Resort, Murang'a",
    website: "www.ikia-conference.org",
    email: "<EMAIL>",
    phone: "+254 700 000 000",
  }

  const nextSteps = [
    {
      title: "Check Your Email",
      description: "A confirmation email with your registration details and receipt has been sent to your email address.",
      icon: Mail,
    },
    {
      title: "Download Materials",
      description: "Access your registration certificate and conference materials from the downloads section.",
      icon: Download,
    },
    {
      title: "Mark Your Calendar",
      description: "Add the conference dates to your calendar and start planning your attendance.",
      icon: Calendar,
    },
    {
      title: "Stay Updated",
      description: "Follow our website and social media for the latest updates and announcements.",
      icon: Share2,
    },
  ]

  const handleDownloadCertificate = () => {
    // In a real app, this would generate and download the certificate
    console.log("Downloading registration certificate...")
  }

  const handleDownloadReceipt = () => {
    // In a real app, this would generate and download the payment receipt
    console.log("Downloading payment receipt...")
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Success Header */}
      <div className="text-center mb-12">
        <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-green-500 to-green-600 rounded-full mb-6 shadow-2xl">
          <CheckCircle className="w-12 h-12 text-white" />
        </div>
        
        <h1 className="text-4xl font-bold text-gray-900 mb-4" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
          Registration Successful!
        </h1>
        
        <p className="text-xl text-gray-600 mb-2">
          Thank you for registering for the IKIA Conference 2025
        </p>
        
        <div className="inline-flex items-center space-x-2 bg-green-100 text-green-800 px-4 py-2 rounded-full">
          <span className="font-semibold">Confirmation #:</span>
          <span className="font-mono">{registrationDetails.confirmationNumber}</span>
        </div>
      </div>

      <div className="space-y-8">
        {/* Registration Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Registration Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Registrant</h3>
                <p className="text-gray-700">{registrationDetails.registrantName}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Email</h3>
                <p className="text-gray-700">{registrationDetails.email}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Registration Type</h3>
                <p className="text-gray-700">{registrationDetails.registrationType}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Package</h3>
                <p className="text-gray-700">{registrationDetails.package}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Amount Paid</h3>
                <p className="text-green-600 font-bold text-lg">{registrationDetails.amount}</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Payment Method</h3>
                <p className="text-gray-700">{registrationDetails.paymentMethod}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Conference Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Conference Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">{conferenceDetails.name}</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center space-x-3">
                <Calendar className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="font-semibold text-gray-900">Dates</h4>
                  <p className="text-gray-700">{conferenceDetails.dates}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <MapPin className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="font-semibold text-gray-900">Venue</h4>
                  <p className="text-gray-700">{conferenceDetails.venue}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="font-semibold text-gray-900">Email</h4>
                  <p className="text-gray-700">{conferenceDetails.email}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-green-600" />
                <div>
                  <h4 className="font-semibold text-gray-900">Phone</h4>
                  <p className="text-gray-700">{conferenceDetails.phone}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Downloads */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Downloads
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                onClick={handleDownloadCertificate}
                variant="outline"
                className="flex items-center space-x-2 h-auto p-4"
              >
                <FileText className="w-5 h-5" />
                <div className="text-left">
                  <div className="font-semibold">Registration Certificate</div>
                  <div className="text-sm text-gray-600">PDF format</div>
                </div>
              </Button>
              
              <Button
                onClick={handleDownloadReceipt}
                variant="outline"
                className="flex items-center space-x-2 h-auto p-4"
              >
                <Download className="w-5 h-5" />
                <div className="text-left">
                  <div className="font-semibold">Payment Receipt</div>
                  <div className="text-sm text-gray-600">PDF format</div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              What's Next?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {nextSteps.map((step, index) => {
                const IconComponent = step.icon
                return (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <IconComponent className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">{step.title}</h3>
                      <p className="text-sm text-gray-600">{step.description}</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={() => router.push("/")}
            variant="outline"
            className="px-8 py-3"
          >
            <Home className="w-4 h-4 mr-2" />
            Return to Home
          </Button>
          
          <Button
            onClick={() => window.print()}
            className="px-8 py-3"
          >
            <FileText className="w-4 h-4 mr-2" />
            Print Confirmation
          </Button>
        </div>
      </div>
    </div>
  )
}
