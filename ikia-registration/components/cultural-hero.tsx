"use client"

import { useState, useEffect } from "react"

export function CulturalHero() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <div className="relative w-full h-64 bg-gradient-to-r from-green-50 via-amber-50 to-orange-50 rounded-2xl overflow-hidden mb-8">
      {/* Cultural Pattern Background */}
      <div className="absolute inset-0 opacity-20">
        <svg width="100%" height="100%" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="cultural-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
              <circle cx="20" cy="20" r="8" fill="#159147" opacity="0.3" />
              <polygon points="20,5 35,20 20,35 5,20" fill="#E8B32C" opacity="0.2" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#cultural-pattern)" />
        </svg>
      </div>

      {/* Content */}
      <div className="relative z-10 flex items-center justify-center h-full">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-800 mb-4" style={{ fontFamily: "Myriad Pro, sans-serif" }}>
            <span className="bg-gradient-to-r from-green-700 to-amber-600 bg-clip-text text-transparent">
              Indigenous Knowledge
            </span>
          </h2>
          <p className="text-lg text-gray-600">Preserving wisdom • Fostering innovation • Building futures</p>
        </div>
      </div>

      {/* Floating Cultural Elements */}
      <div className="absolute top-4 left-8 w-4 h-4 bg-green-500 rounded-full animate-bounce opacity-60"></div>
      <div
        className="absolute top-12 right-12 w-3 h-3 bg-yellow-500 rounded-full animate-bounce opacity-60"
        style={{ animationDelay: "0.5s" }}
      ></div>
      <div
        className="absolute bottom-8 left-16 w-5 h-5 bg-orange-500 rounded-full animate-bounce opacity-60"
        style={{ animationDelay: "1s" }}
      ></div>
      <div
        className="absolute bottom-4 right-8 w-2 h-2 bg-red-500 rounded-full animate-bounce opacity-60"
        style={{ animationDelay: "1.5s" }}
      ></div>
    </div>
  )
}
