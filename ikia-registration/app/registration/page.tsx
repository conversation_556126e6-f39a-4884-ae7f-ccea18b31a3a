"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  Users,
  Store,
  TrendingUp,
  Crown,
  Building2,
  ArrowRight,
  Check,
  Sparkles,
  Calendar,
  MapPin,
  Globe,
  Target,
  Award,
  Handshake,
} from "lucide-react"

const userTypes = [
  {
    id: "guest",
    title: "Conference Delegate",
    subtitle: "Join the Knowledge Exchange",
    description:
      "Participate in sessions, workshops, and networking to explore Indigenous Knowledge innovations and investment opportunities.",
    icon: Users,
    color: "green",
    gradient: "from-green-600 to-green-700",
    features: [
      "Access to all conference sessions and workshops",
      "Site visits and exhibition area access",
      "Gift pack with branded items and cultural souvenir",
      "Conference materials and program booklet",
      "Tea breaks and lunch for all days",
      "Certificate of participation",
    ],
    popular: true,
    badge: "Most Popular",
  },
  {
    id: "exhibitor",
    title: "Trade Fair Exhibitor",
    subtitle: "Showcase Your Innovation",
    description:
      "Display your Indigenous Knowledge-based products and services to investors, buyers, and international partners.",
    icon: Store,
    color: "brown",
    gradient: "from-amber-700 to-amber-800",
    features: [
      "Standard 3x3m booth with setup support",
      "Delegate passes for 2 people for 3 days",
      "Access to dealrooms and networking sessions",
      "Media coverage and promotional opportunities",
      "Gift hamper and dinner inclusion",
      "Certificate of participation",
    ],
    popular: false,
    badge: "Best Value",
  },
  {
    id: "investor",
    title: "Investment Discovery",
    subtitle: "Explore Opportunities",
    description:
      "Discover Indigenous Knowledge investment opportunities and connect with innovative enterprises as a delegate with investment interests.",
    icon: TrendingUp,
    color: "ochre",
    gradient: "from-yellow-600 to-orange-600",
    features: [
      "Access to investment showcase sessions",
      "Networking with IKIA holders and entrepreneurs",
      "Market intelligence and due diligence support",
      "Investment opportunity presentations",
      "Post-conference follow-up and connections",
      "All standard delegate benefits included",
    ],
    popular: false,
    badge: "Investment Focus",
  },
  {
    id: "vip",
    title: "VIP Delegate",
    subtitle: "Premium Experience",
    description:
      "Exclusive access for dignitaries, keynote speakers, government officials, and distinguished guests with premium amenities.",
    icon: Crown,
    color: "blue",
    gradient: "from-blue-600 to-blue-700",
    features: [
      "Access to VIP dealrooms and holding areas",
      "Gala dinner and exclusive networking events",
      "Priority registration desk and VIP car badge",
      "Premium gift pack with flash drive",
      "All delegate benefits plus VIP amenities",
      "Downloadable VIP car sticker upon payment",
    ],
    popular: false,
    badge: "Premium",
  },
  {
    id: "sponsor",
    title: "Conference Sponsor",
    subtitle: "Partner for Impact",
    description:
      "Support Indigenous Knowledge innovation while gaining brand visibility, thought leadership, and strategic networking opportunities.",
    icon: Building2,
    color: "sienna",
    gradient: "from-orange-700 to-red-700",
    features: [
      "Brand visibility across all conference materials",
      "Speaking opportunities and thought leadership",
      "Premium networking privileges and access",
      "Custom partnership packages available",
      "Year-round association with IKIA initiative",
      "Multiple sponsorship tiers available",
    ],
    popular: false,
    badge: "Partnership",
  },
]

const conferenceStats = [
  { icon: Globe, label: "Countries Represented", value: "13+" },
  { icon: Target, label: "IKIA Projects", value: "100+" },
  { icon: Award, label: "Innovation Categories", value: "6" },
  { icon: Handshake, label: "Expected Attendees", value: "500+" },
]

export default function UserTypeSelection() {
  const [selectedType, setSelectedType] = useState<string>("")
  const [hoveredType, setHoveredType] = useState<string>("")
  const router = useRouter()

  const handleContinue = () => {
    if (selectedType) {
      router.push(`/registration/form/${selectedType}`)
    }
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* Enhanced Hero Section */}
      <div className="text-center mb-16 relative">
        <div className="relative bg-gradient-to-br from-white via-green-50 via-yellow-50 to-orange-50 rounded-3xl p-12 shadow-2xl border border-gray-100 overflow-hidden">
          {/* Heritage Decorative Elements */}
          <div className="absolute top-8 left-12 w-8 h-8 bg-green-500 rounded-full opacity-20 animate-african-dance"></div>
          <div
            className="absolute top-20 right-16 w-6 h-6 bg-yellow-500 rounded-full opacity-30 animate-african-dance"
            style={{ animationDelay: "2s" }}
          ></div>
          <div
            className="absolute bottom-12 left-20 w-10 h-10 bg-orange-500 rounded-full opacity-25 animate-african-dance"
            style={{ animationDelay: "4s" }}
          ></div>

          <div className="relative z-10">
            {/* Enhanced Logo */}
            <div className="inline-flex items-center justify-center w-24 h-24 gradient-primary rounded-3xl mb-8 shadow-2xl relative overflow-hidden">
              <Sparkles className="w-12 h-12 text-white relative z-10" />
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-heritage-pulse"></div>
            </div>

            <h1 className="heading-display text-gray-900 mb-6">
              Choose Your <span className="text-gradient-heritage">Registration Type</span>
            </h1>

            <div className="max-w-4xl mx-auto mb-8">
              <p className="body-large text-gray-700 mb-6">
                Join the{" "}
                <strong className="text-green-700">1st International Investment Conference and Trade Fair</strong> on{" "}
                <strong className="text-yellow-700">Indigenous Knowledge Intellectual Assets</strong>
              </p>

              <div className="flex flex-wrap justify-center items-center gap-6 text-gray-600 mb-6">
                <div className="flex items-center space-x-3 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-md">
                  <Calendar className="w-5 h-5 text-green-600" />
                  <span className="body-base font-semibold">November 18-21, 2025</span>
                </div>
                <div className="flex items-center space-x-3 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full shadow-md">
                  <MapPin className="w-5 h-5 text-yellow-600" />
                  <span className="body-base font-semibold">Thika Greens Golf Resort, Murang'a</span>
                </div>
              </div>

              <p className="body-base text-gray-600 italic">
                "Where <span className="text-green-600 font-semibold">ancestral wisdom</span> meets{" "}
                <span className="text-blue-600 font-semibold">future innovation</span>"
              </p>
            </div>

            {/* Conference Statistics */}
            <div className="grid grid-even grid-even-4 max-w-4xl mx-auto">
              {conferenceStats.map((stat, index) => {
                const IconComponent = stat.icon
                return (
                  <div key={index} className="text-center bg-white/60 backdrop-blur-sm rounded-2xl p-4 shadow-md">
                    <div className="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center mx-auto mb-3">
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <p className="heading-4 text-gray-900 mb-1">{stat.value}</p>
                    <p className="body-small text-gray-600">{stat.label}</p>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Registration Types Grid - Even Distribution */}
      <div className="grid grid-even grid-even-3 mb-16">
        {userTypes.map((type, index) => {
          const IconComponent = type.icon
          const isSelected = selectedType === type.id
          const isHovered = hoveredType === type.id

          return (
            <Card
              key={type.id}
              className={`
                card-heritage cursor-pointer transition-all duration-500 overflow-hidden group relative
                ${
                  isSelected
                    ? "ring-4 ring-green-500 shadow-2xl transform -translate-y-4 bg-gradient-to-br from-white to-green-50"
                    : "hover:shadow-2xl hover:-translate-y-2 bg-white"
                }
                ${type.popular ? "border-2 border-yellow-400" : ""}
              `}
              onClick={() => setSelectedType(type.id)}
              onMouseEnter={() => setHoveredType(type.id)}
              onMouseLeave={() => setHoveredType("")}
            >
              {/* Badge */}
              <div className="absolute top-4 right-4 z-20">
                <div
                  className={`
                  px-3 py-1 rounded-full text-xs font-semibold
                  ${
                    type.popular
                      ? "bg-gradient-to-r from-yellow-400 to-orange-400 text-white"
                      : "bg-gray-100 text-gray-600"
                  }
                `}
                >
                  {type.badge}
                </div>
              </div>

              <CardContent className="p-8 relative z-10">
                {/* Icon and Title Section */}
                <div className="mb-8">
                  <div className="relative inline-block mb-6">
                    <div
                      className={`
                      w-20 h-20 rounded-2xl flex items-center justify-center shadow-xl transition-all duration-500
                      bg-gradient-to-br ${type.gradient}
                      ${isSelected || isHovered ? "scale-110 rotate-3" : ""}
                    `}
                    >
                      <IconComponent className="w-10 h-10 text-white" />
                    </div>
                    {isSelected && (
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
                        <Check className="w-5 h-5 text-white" />
                      </div>
                    )}
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-yellow-400 rounded-full animate-heritage-pulse"></div>
                  </div>

                  <h3 className="heading-3 text-gray-900 mb-3">{type.title}</h3>
                  <p className="body-base text-green-600 font-semibold mb-4">{type.subtitle}</p>
                  <p className="body-base text-gray-600 leading-relaxed">{type.description}</p>
                </div>

                {/* Features List */}
                <div className="space-y-3 mb-6">
                  {type.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start space-x-3">
                      <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Check className="w-3 h-3 text-green-600" />
                      </div>
                      <span className="body-small text-gray-700 leading-relaxed">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Selection Indicator */}
                {isSelected && (
                  <div className="flex items-center justify-center space-x-2 text-green-600 bg-green-50 px-4 py-2 rounded-full">
                    <Check className="w-5 h-5" />
                    <span className="body-small font-semibold">Selected</span>
                  </div>
                )}
              </CardContent>

              {/* Selection Indicator Bar */}
              <div
                className={`
                absolute bottom-0 left-0 right-0 h-2 transition-all duration-500
                ${
                  isSelected
                    ? `bg-gradient-to-r ${type.gradient} scale-x-100`
                    : "bg-gradient-to-r from-gray-200 to-gray-300 scale-x-0 group-hover:scale-x-100"
                }
              `}
              ></div>
            </Card>
          )
        })}
      </div>

      {/* Enhanced Continue Button */}
      <div className="text-center relative">
        <div className="inline-block relative">
          <Button
            onClick={handleContinue}
            disabled={!selectedType}
            className={`
              btn-primary px-16 py-6 text-xl font-bold relative overflow-hidden group
              disabled:opacity-50 disabled:cursor-not-allowed shadow-2xl
              ${selectedType ? "animate-fade-in-up" : ""}
            `}
            style={{ fontFamily: "var(--font-body)" }}
          >
            <span className="relative z-10 flex items-center space-x-4">
              <span>Continue to Registration</span>
              <ArrowRight className="w-6 h-6 transition-transform group-hover:translate-x-2" />
            </span>

            <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
          </Button>

          {/* Heritage Decorative Elements */}
          {selectedType && (
            <>
              <div className="absolute -top-4 -left-4 w-4 h-4 bg-green-500 rounded-full animate-bounce"></div>
              <div
                className="absolute -top-4 -right-4 w-3 h-3 bg-yellow-500 rounded-full animate-bounce"
                style={{ animationDelay: "0.2s" }}
              ></div>
              <div
                className="absolute -bottom-4 -left-4 w-3 h-3 bg-orange-500 rounded-full animate-bounce"
                style={{ animationDelay: "0.4s" }}
              ></div>
              <div
                className="absolute -bottom-4 -right-4 w-4 h-4 bg-red-500 rounded-full animate-bounce"
                style={{ animationDelay: "0.6s" }}
              ></div>
            </>
          )}
        </div>

        {/* Selection Guidance */}
        {!selectedType && (
          <div className="mt-8 animate-fade-in-up">
            <p className="body-base text-gray-500 mb-4">Please select a registration type to continue</p>
            <div className="flex justify-center items-center space-x-4">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <div className="w-8 h-0.5 bg-gradient-to-r from-green-500 to-yellow-500"></div>
              <div
                className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"
                style={{ animationDelay: "0.5s" }}
              ></div>
              <div className="w-8 h-0.5 bg-gradient-to-r from-yellow-500 to-orange-500"></div>
              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse" style={{ animationDelay: "1s" }}></div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
