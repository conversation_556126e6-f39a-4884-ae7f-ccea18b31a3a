"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { usePathname } from "next/navigation"
import { CheckCircle2, Circle } from "lucide-react"

const steps = [
  { id: 1, name: "Select Type", path: "/registration" },
  { id: 2, name: "Details", path: "/registration/form" },
  { id: 3, name: "Review", path: "/registration/review" },
  { id: 4, name: "Complete", path: "/registration/success" },
]

export default function RegistrationLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const [currentStep, setCurrentStep] = useState(1)

  useEffect(() => {
    if (pathname === "/registration") setCurrentStep(1)
    else if (pathname.includes("/form")) setCurrentStep(2)
    else if (pathname.includes("/review")) setCurrentStep(3)
    else if (pathname.includes("/success")) setCurrentStep(4)
  }, [pathname])

  return (
    <div className="min-h-screen bg-white">
      {/* Simplified Progress Indicator */}
      <div className="bg-gradient-to-br from-gray-50 via-green-50 to-yellow-50 border-b border-gray-100">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center mb-6">
            <h2 className="heading-3 text-gray-900 mb-2">Registration Progress</h2>
            <p className="body-base text-gray-600">
              Step {currentStep} of {steps.length}
            </p>
          </div>

          {/* Simplified Progress Steps */}
          <div className="relative">
            {/* Progress Line */}
            <div className="absolute top-6 left-0 right-0 h-1 bg-gray-200 rounded-full">
              <div
                className="h-full gradient-primary rounded-full transition-all duration-1000 ease-out"
                style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
              ></div>
            </div>

            <div className="relative flex justify-between">
              {steps.map((step, index) => {
                const isCompleted = currentStep > step.id
                const isCurrent = currentStep === step.id

                return (
                  <div key={step.id} className="flex flex-col items-center">
                    {/* Step Circle */}
                    <div
                      className={`
                        relative w-12 h-12 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-500 z-10
                        ${
                          isCompleted
                            ? "gradient-primary text-white shadow-lg scale-110"
                            : isCurrent
                              ? "bg-white text-green-600 border-4 border-green-600 shadow-lg scale-110"
                              : "bg-gray-100 text-gray-400 border-2 border-gray-200"
                        }
                      `}
                    >
                      {isCompleted ? (
                        <CheckCircle2 className="w-6 h-6" />
                      ) : isCurrent ? (
                        <div className="w-4 h-4 gradient-primary rounded-full animate-heritage-pulse"></div>
                      ) : (
                        <Circle className="w-5 h-5" />
                      )}
                    </div>

                    {/* Step Label */}
                    <div className="mt-3 text-center">
                      <p
                        className={`
                          body-small font-semibold transition-colors
                          ${isCompleted || isCurrent ? "text-green-700" : "text-gray-500"}
                        `}
                        style={{ fontFamily: "var(--font-body)" }}
                      >
                        {step.name}
                      </p>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="flex-1 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">{children}</div>
      </main>
    </div>
  )
}
