"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { CheckCircle, Download, Calendar, Mail, Smartphone, QrCode } from "lucide-react"

export default function RegistrationSuccess() {
  const [registrationData, setRegistrationData] = useState<any>(null)
  const [qrCodeGenerated, setQrCodeGenerated] = useState(false)

  useEffect(() => {
    const data = localStorage.getItem("registrationData")
    if (data) {
      setRegistrationData(JSON.parse(data))
    }
  }, [])

  const getUserTypeDisplay = (type: string) => {
    const types: { [key: string]: string } = {
      guest: "Conference Delegate",
      exhibitor: "Trade Fair Exhibitor",
      investor: "Investment Discovery Delegate",
      vip: "VIP Delegate",
      sponsor: "Conference Sponsor",
    }
    return types[type] || type
  }

  const generateQRCode = () => {
    // Simulate QR code generation
    setQrCodeGenerated(true)

    // Create QR code data
    const qrData = {
      name: registrationData?.fullName,
      email: registrationData?.email,
      type: registrationData?.type,
      conference: "IKIA 2025",
      registrationId: `IKIA-${Date.now()}`,
    }

    // In a real implementation, this would generate an actual QR code
    const qrCodeUrl = `data:image/svg+xml;base64,${btoa(`
      <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
        <rect width="200" height="200" fill="white"/>
        <rect x="20" y="20" width="160" height="160" fill="black"/>
        <rect x="40" y="40" width="120" height="120" fill="white"/>
        <text x="100" y="105" textAnchor="middle" fontFamily="Arial" fontSize="12" fill="black">
          QR Code
        </text>
        <text x="100" y="125" textAnchor="middle" fontFamily="Arial" fontSize="8" fill="black">
          ${qrData.registrationId}
        </text>
      </svg>
    `)}`

    // Create download link
    const link = document.createElement("a")
    link.href = qrCodeUrl
    link.download = `IKIA-2025-QR-${registrationData?.fullName?.replace(/\s+/g, "-")}.svg`
    link.click()
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <div className="inline-flex items-center justify-center w-20 h-20 gradient-primary rounded-full mb-6">
          <CheckCircle className="w-10 h-10 text-white" />
        </div>
        <h1 className="heading-1 text-gray-900 mb-4">Registration Successful!</h1>
        <p className="body-large text-gray-600 max-w-2xl mx-auto">
          Thank you for registering for the 1st International Investment Conference and Trade Fair on Indigenous
          Knowledge Intellectual Assets (IKIA) 2025.
        </p>
      </div>

      {/* Registration Summary */}
      {registrationData && (
        <Card className="mb-8 card-modern">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="heading-4 text-gray-900">Registration Summary</h3>
              <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                {getUserTypeDisplay(registrationData.type)}
              </span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="body-small text-gray-600">Name</p>
                <p className="body-base font-medium">{registrationData.fullName}</p>
              </div>
              <div>
                <p className="body-small text-gray-600">Email</p>
                <p className="body-base font-medium">{registrationData.email}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* QR Code Download Section */}
      <Card className="mb-8 card-heritage bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardContent className="p-6">
          <div className="flex items-center mb-4">
            <div className="w-10 h-10 gradient-primary rounded-lg flex items-center justify-center mr-3">
              <QrCode className="w-5 h-5 text-white" />
            </div>
            <h3 className="heading-4 text-green-900">Your Registration QR Code</h3>
          </div>
          <p className="text-green-700 mb-4">
            Download your personalized QR code for quick check-in at the conference. This QR code contains your
            registration details and will speed up the registration process at the venue.
          </p>
          <div className="flex items-center space-x-4">
            <Button onClick={generateQRCode} className="btn-primary">
              <Download className="w-4 h-4 mr-2" />
              Download QR Code
            </Button>
            {qrCodeGenerated && (
              <div className="flex items-center text-green-600">
                <CheckCircle className="w-4 h-4 mr-2" />
                <span className="body-small font-medium">QR Code Downloaded</span>
              </div>
            )}
          </div>
          <p className="body-small text-green-600 mt-2">
            💡 Tip: Save the QR code to your phone for easy access during check-in
          </p>
        </CardContent>
      </Card>

      {/* Next Steps */}
      <div className="grid grid-even grid-even-2 mb-8">
        <Card className="card-modern hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <Mail className="w-5 h-5 text-blue-600" />
              </div>
              <h3 className="heading-4">Check Your Email</h3>
            </div>
            <p className="body-base text-gray-600 mb-4">
              A confirmation email has been sent to {registrationData?.email}. Please check your spam folder if you
              don't see it.
            </p>
            <p className="body-small text-gray-500">
              The email contains your registration details, terms & conditions summary, and further instructions.
            </p>
          </CardContent>
        </Card>

        <Card className="card-modern hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <Calendar className="w-5 h-5 text-green-600" />
              </div>
              <h3 className="heading-4">Mark Your Calendar</h3>
            </div>
            <p className="body-base text-gray-600 mb-4">
              IKIA 2025 Conference
              <br />
              <strong>November 18-21, 2025</strong>
              <br />
              Thika Greens Golf Resort, Murang'a
            </p>
            <div className="space-y-2">
              <a
                href="data:text/calendar;charset=utf8,BEGIN:VCALENDAR
VERSION:2.0
BEGIN:VEVENT
DTSTART:20251118T080000Z
DTEND:20251121T180000Z
SUMMARY:IKIA 2025 Conference
DESCRIPTION:1st International Investment Conference and Trade Fair on Indigenous Knowledge Intellectual Assets
LOCATION:Thika Greens Golf Resort, Murang'a, Kenya
END:VEVENT
END:VCALENDAR"
                download="ikia-2025-conference.ics"
                className="inline-block"
              >
                <Button variant="outline" size="sm" className="w-full bg-transparent">
                  <Download className="w-4 h-4 mr-2" />
                  Add to Calendar
                </Button>
              </a>
              <a
                href="https://calendar.google.com/calendar/render?action=TEMPLATE&text=IKIA%202025%20Conference&dates=20251118T080000Z/20251121T180000Z&details=1st%20International%20Investment%20Conference%20and%20Trade%20Fair%20on%20Indigenous%20Knowledge%20Intellectual%20Assets&location=Thika%20Greens%20Golf%20Resort,%20Murang'a,%20Kenya"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Button variant="outline" size="sm" className="w-full bg-transparent">
                  <Calendar className="w-4 h-4 mr-2" />
                  Google Calendar
                </Button>
              </a>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8 mb-8">
        <h3 className="heading-4 text-gray-900 mb-6 text-center">What's Next?</h3>
        <div className="grid grid-even grid-even-3">
          <Link href="/agenda" className="block">
            <div className="bg-white rounded-xl p-6 hover:shadow-md transition-shadow text-center">
              <Calendar className="w-8 h-8 text-green-600 mb-3 mx-auto" />
              <h4 className="body-base font-medium text-gray-900 mb-2">View Agenda</h4>
              <p className="body-small text-gray-600">Explore the conference schedule</p>
            </div>
          </Link>

          <Link href="/ikia-showcase" className="block">
            <div className="bg-white rounded-xl p-6 hover:shadow-md transition-shadow text-center">
              <CheckCircle className="w-8 h-8 text-blue-600 mb-3 mx-auto" />
              <h4 className="body-base font-medium text-gray-900 mb-2">IKIA Showcase</h4>
              <p className="body-small text-gray-600">Discover featured innovations</p>
            </div>
          </Link>

          <div className="bg-white rounded-xl p-6 hover:shadow-md transition-shadow text-center">
            <Mail className="w-8 h-8 text-purple-600 mb-3 mx-auto" />
            <h4 className="body-base font-medium text-gray-900 mb-2">Contact Support</h4>
            <div className="body-small text-gray-600 space-y-1">
              <p>Email: <EMAIL></p>
              <p>Phone: +254 700 123 456</p>
              <p>Hours: Mon-Fri, 9:00 AM - 5:00 PM</p>
            </div>
          </div>
        </div>
      </div>

      {/* VIP Car Sticker Download */}
      {registrationData?.type === "vip" && (
        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200 mb-8">
          <CardContent className="p-6">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <Download className="w-5 h-5 text-purple-600" />
              </div>
              <h3 className="heading-4 text-purple-900">VIP Car Sticker Available</h3>
            </div>
            <p className="text-purple-700 mb-4">
              Your VIP car sticker is ready for download. Print and display it on your vehicle for easy identification
              and parking privileges at Thika Greens Golf Resort.
            </p>
            <Button className="bg-purple-600 hover:bg-purple-700 text-white">
              <Download className="w-4 h-4 mr-2" />
              Download VIP Sticker
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Conference App Download */}
      <Card className="gradient-primary text-white mb-8">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="heading-4 mb-2">Download the IKIA 2025 App</h3>
              <p className="text-green-100">
                Stay connected with real-time updates, networking opportunities, and exclusive content during the
                conference.
              </p>
            </div>
            <div className="flex space-x-3">
              <Button variant="secondary" size="sm">
                <Smartphone className="w-4 h-4 mr-2" />
                iOS
              </Button>
              <Button variant="secondary" size="sm">
                <Smartphone className="w-4 h-4 mr-2" />
                Android
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Return Home */}
      <div className="text-center">
        <Link href="/">
          <Button variant="outline" size="lg" className="btn-secondary bg-transparent">
            Return to Homepage
          </Button>
        </Link>
      </div>
    </div>
  )
}
