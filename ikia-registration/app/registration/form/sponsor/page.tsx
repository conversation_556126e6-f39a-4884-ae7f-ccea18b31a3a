"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Building2, Upload, Check, Plus } from "lucide-react"

const industries = [
  "Technology",
  "Finance",
  "Agriculture",
  "Renewable Energy",
  "Cultural Heritage",
  "Government",
  "NGO",
  "Other",
]

const countries = [
  "Kenya",
  "Uganda",
  "Tanzania",
  "Rwanda",
  "Burundi",
  "South Sudan",
  "Ethiopia",
  "Somalia",
  "United States",
  "United Kingdom",
  "Other",
]

const paymentMethods = ["Bank Transfer", "Credit Card", "Mobile Money", "Other"]

const sponsorshipTiers = [
  {
    id: "platinum",
    name: "Platinum Sponsor",
    amount: "KES 5,000,000",
    usdAmount: "USD 38,000",
    benefits: [
      "Prominent logo placement on all materials",
      "Keynote speaking slot (15-20 minutes)",
      "Large exhibition space (6x6m)",
      "10 VIP delegate passes",
      "Exclusive branding of conference area",
      "Full-page advertisement in program",
      "Pre/post-event attendee list",
      "Dedicated social media campaign",
      "Private side event opportunity",
    ],
  },
  {
    id: "gold",
    name: "Gold Sponsor",
    amount: "KES 2,500,000",
    usdAmount: "USD 19,000",
    benefits: [
      "Large logo placement on materials",
      "Speaking opportunity in breakout session",
      "Standard exhibition space (3x3m)",
      "5 VIP delegate passes",
      "Session/workshop branding",
      "Half-page advertisement in program",
      "Social media mentions",
    ],
  },
  {
    id: "silver",
    name: "Silver Sponsor",
    amount: "KES 1,000,000",
    usdAmount: "USD 7,600",
    benefits: [
      "Medium logo placement on materials",
      "Shared exhibition space (2x2m)",
      "3 delegate passes",
      "Website sponsor page inclusion",
      "Quarter-page advertisement in program",
    ],
  },
  {
    id: "bronze",
    name: "Bronze Sponsor",
    amount: "KES 500,000",
    usdAmount: "USD 3,800",
    benefits: ["Small logo placement on website", "1 delegate pass", "Mention in conference program"],
  },
]

export default function SponsorRegistration() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    // Contact Details
    fullName: "",
    email: "",
    phone: "",
    jobTitle: "",
    // Company Details
    companyName: "",
    website: "",
    description: "",
    industry: "",
    country: "",
    // Sponsorship
    sponsorshipLevel: "",
    paymentMethod: "",
    specialRequests: "",
    agreedToTerms: false,
    countryCode: "+254",
    contactRole: "",
    alternateContact: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    localStorage.setItem("registrationData", JSON.stringify({ type: "sponsor", ...formData }))
    router.push("/registration/review")
  }

  const selectedTier = sponsorshipTiers.find((tier) => tier.id === formData.sponsorshipLevel)

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center mb-8">
        <Button variant="ghost" onClick={() => router.back()} className="mr-4">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <div className="flex items-center">
          <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-red-600 rounded-lg flex items-center justify-center mr-3">
            <Building2 className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Sponsor Registration</h1>
            <p className="text-gray-600">Partner with us to support Indigenous Knowledge innovation</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Contact Details */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Your Contact Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="fullName">Full Name *</Label>
                <Input
                  id="fullName"
                  value={formData.fullName}
                  onChange={(e) => setFormData((prev) => ({ ...prev, fullName: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <div className="flex mt-1">
                  <Select onValueChange={(value) => setFormData((prev) => ({ ...prev, countryCode: value }))}>
                    <SelectTrigger className="w-24">
                      <SelectValue placeholder="+254" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="+254">+254</SelectItem>
                      <SelectItem value="+256">+256</SelectItem>
                      <SelectItem value="+255">+255</SelectItem>
                      <SelectItem value="+250">+250</SelectItem>
                      <SelectItem value="+1">+1</SelectItem>
                      <SelectItem value="+44">+44</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData((prev) => ({ ...prev, phone: e.target.value }))}
                    required
                    className="ml-2 flex-1"
                    placeholder="712345678"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="jobTitle">Job Title *</Label>
                <Input
                  id="jobTitle"
                  value={formData.jobTitle}
                  onChange={(e) => setFormData((prev) => ({ ...prev, jobTitle: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Company Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Company Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="companyName">Company Name *</Label>
                <Input
                  id="companyName"
                  value={formData.companyName}
                  onChange={(e) => setFormData((prev) => ({ ...prev, companyName: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="website">Company Website</Label>
                <Input
                  id="website"
                  type="url"
                  value={formData.website}
                  onChange={(e) => setFormData((prev) => ({ ...prev, website: e.target.value }))}
                  className="mt-1"
                  placeholder="https://www.example.com"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Company Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                required
                className="mt-1"
                placeholder="Describe your business and interest in Indigenous Knowledge Intellectual Assets"
                rows={4}
                maxLength={1000}
              />
              <p className="text-sm text-gray-500 mt-1">{formData.description.length}/1000 characters</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="industry">Company Industry *</Label>
                <Select onValueChange={(value) => setFormData((prev) => ({ ...prev, industry: value }))}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select your industry" />
                  </SelectTrigger>
                  <SelectContent>
                    {industries.map((industry) => (
                      <SelectItem key={industry} value={industry}>
                        {industry}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="country">Country of Headquarters *</Label>
                <Select onValueChange={(value) => setFormData((prev) => ({ ...prev, country: value }))}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="logo">Upload Company Logo</Label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-green-400 transition-colors">
                <div className="space-y-1 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600">
                    <label
                      htmlFor="logo-upload"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500"
                    >
                      <span>Upload a file</span>
                      <input id="logo-upload" name="logo-upload" type="file" className="sr-only" accept="image/*" />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500">PNG, JPG, SVG up to 5MB</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Primary Contact Person</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Existing contact fields */}

            <div>
              <Label htmlFor="contactRole">Role/Responsibility *</Label>
              <Input
                id="contactRole"
                value={formData.contactRole}
                onChange={(e) => setFormData((prev) => ({ ...prev, contactRole: e.target.value }))}
                required
                className="mt-1"
                placeholder="e.g., Marketing Manager, Partnership Director"
              />
            </div>

            <div>
              <Label htmlFor="alternateContact">Alternate Contact Person</Label>
              <Input
                id="alternateContact"
                value={formData.alternateContact}
                onChange={(e) => setFormData((prev) => ({ ...prev, alternateContact: e.target.value }))}
                className="mt-1"
                placeholder="Name and phone number of alternate contact"
              />
            </div>
          </CardContent>
        </Card>

        {/* Sponsorship Level Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Select Your Sponsorship Level</CardTitle>
            <p className="text-gray-600">
              Choose the sponsorship tier that best fits your organization's goals and budget.
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label className="text-base font-medium">Select Your Sponsorship Level *</Label>
              <div className="mt-4 space-y-4">
                {sponsorshipTiers.map((tier, index) => (
                  <div
                    key={tier.id}
                    className={`p-6 border-2 rounded-lg cursor-pointer transition-all ${
                      formData.sponsorshipLevel === tier.id
                        ? "border-green-500 bg-green-50 shadow-md"
                        : "border-gray-200 hover:border-green-300"
                    }`}
                    onClick={() => setFormData((prev) => ({ ...prev, sponsorshipLevel: tier.id }))}
                  >
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-semibold text-gray-900">{tier.name}</h4>
                      <div className="text-right">
                        <p className="text-2xl font-bold text-green-600">{tier.amount}</p>
                        <p className="text-sm text-gray-500">({tier.usdAmount})</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {tier.benefits.slice(0, 4).map((benefit, idx) => (
                        <div key={idx} className="flex items-start space-x-2">
                          <Check className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {selectedTier && (
              <div className="bg-green-50 p-4 rounded-lg">
                <h5 className="font-medium text-green-800 mb-2">Selected: {selectedTier.name}</h5>
                <p className="text-green-700">
                  Investment: {selectedTier.amount} ({selectedTier.usdAmount})
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Payment & Agreement */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Payment & Agreement</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="paymentMethod">Preferred Payment Method *</Label>
              <Select onValueChange={(value) => setFormData((prev) => ({ ...prev, paymentMethod: value }))}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  {paymentMethods.map((method) => (
                    <SelectItem key={method} value={method}>
                      {method}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-sm text-gray-500 mt-1">
                Final payment processing will be handled separately after form submission
              </p>
            </div>

            <div className="flex items-start space-x-3">
              <Checkbox
                id="terms"
                checked={formData.agreedToTerms}
                onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, agreedToTerms: checked as boolean }))}
              />
              <div className="text-sm">
                <label htmlFor="terms" className="cursor-pointer">
                  I agree to the{" "}
                  <a
                    href="/sponsorship-terms"
                    className="text-green-600 hover:underline"
                    target="_blank"
                    rel="noreferrer"
                  >
                    Sponsorship Terms & Conditions
                  </a>
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Team Members & Representatives</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              Add details for team members who will represent your organization at the conference
            </p>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                /* Add team member logic */
              }}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Team Member
            </Button>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Back
          </Button>
          <Button
            type="submit"
            disabled={!formData.agreedToTerms || !formData.sponsorshipLevel}
            className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50"
          >
            Continue to Review
          </Button>
        </div>
      </form>
    </div>
  )
}
