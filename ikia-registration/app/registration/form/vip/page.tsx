"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Crown, Camera, Download, Star } from "lucide-react"

const countries = [
  "Kenya",
  "Uganda",
  "Tanzania",
  "Rwanda",
  "Burundi",
  "South Sudan",
  "Ethiopia",
  "Somalia",
  "United States",
  "United Kingdom",
  "Other",
]

const vipCategories = [
  "Government Official",
  "Keynote Speaker",
  "Distinguished Guest",
  "Diplomatic Representative",
  "Senior Executive",
  "Academic Leader",
  "Cultural Leader",
  "Other Dignitary",
]

const accessibilityOptions = [
  "Wheelchair accessibility",
  "Sign language interpretation",
  "Large print materials",
  "Audio assistance",
  "Dietary accommodations",
  "Personal assistance",
  "VIP transportation",
  "Other accessibility needs",
]

export default function VIPRegistration() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    // Personal Details
    fullName: "",
    email: "",
    phone: "",
    jobTitle: "",
    organization: "",
    country: "",
    // VIP Details
    vipCategory: "",
    invitationCode: "",
    specialRequests: "",
    // Accessibility
    accessibilityNeeds: [] as string[],
    otherAccessibility: "",
    // Agreement
    agreedToTerms: false,
    // Photo
    profilePhoto: null as File | null,
  })

  const handleAccessibilityChange = (need: string, checked: boolean) => {
    if (checked) {
      setFormData((prev) => ({
        ...prev,
        accessibilityNeeds: [...prev.accessibilityNeeds, need],
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        accessibilityNeeds: prev.accessibilityNeeds.filter((item) => item !== need),
      }))
    }
  }

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setFormData((prev) => ({ ...prev, profilePhoto: file }))
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    localStorage.setItem("registrationData", JSON.stringify({ type: "vip", ...formData }))
    router.push("/registration/review")
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center mb-8">
        <Button variant="ghost" onClick={() => router.back()} className="mr-4">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <div className="flex items-center">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
            <Crown className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">VIP Delegate Registration</h1>
            <p className="text-gray-600">Exclusive access for distinguished guests and dignitaries</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* VIP Welcome */}
        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center mb-4">
              <Star className="w-8 h-8 text-purple-600 mr-3" />
              <h3 className="text-xl font-semibold text-purple-900">Welcome, Distinguished Guest</h3>
            </div>
            <p className="text-purple-800 mb-4">
              Thank you for your interest in the IKIA 2025 Conference. As a VIP delegate, you'll enjoy exclusive access
              to premium amenities, networking opportunities, and special events.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-purple-700">
              <div className="flex items-center">
                <Crown className="w-4 h-4 mr-2" />
                VIP lounge access
              </div>
              <div className="flex items-center">
                <Crown className="w-4 h-4 mr-2" />
                Gala dinner inclusion
              </div>
              <div className="flex items-center">
                <Crown className="w-4 h-4 mr-2" />
                Priority registration desk
              </div>
              <div className="flex items-center">
                <Crown className="w-4 h-4 mr-2" />
                VIP car badge & sticker
              </div>
            </div>
          </CardContent>
        </Card>

        {/* VIP Verification */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">VIP Verification</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="vipCategory">VIP Category *</Label>
                <Select onValueChange={(value) => setFormData((prev) => ({ ...prev, vipCategory: value }))}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select your category" />
                  </SelectTrigger>
                  <SelectContent>
                    {vipCategories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="invitationCode">Invitation Code</Label>
                <Input
                  id="invitationCode"
                  value={formData.invitationCode}
                  onChange={(e) => setFormData((prev) => ({ ...prev, invitationCode: e.target.value }))}
                  className="mt-1"
                  placeholder="Enter your invitation code if provided"
                />
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-blue-800 text-sm">
                <strong>Note:</strong> VIP registration may require verification. Our team will contact you within 24
                hours to confirm your VIP status and provide additional details about exclusive events and amenities.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Personal Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="fullName">Full Name *</Label>
                <Input
                  id="fullName"
                  value={formData.fullName}
                  onChange={(e) => setFormData((prev) => ({ ...prev, fullName: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData((prev) => ({ ...prev, phone: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="jobTitle">Title/Position *</Label>
                <Input
                  id="jobTitle"
                  value={formData.jobTitle}
                  onChange={(e) => setFormData((prev) => ({ ...prev, jobTitle: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="organization">Organization/Institution *</Label>
                <Input
                  id="organization"
                  value={formData.organization}
                  onChange={(e) => setFormData((prev) => ({ ...prev, organization: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="country">Country *</Label>
                <Select onValueChange={(value) => setFormData((prev) => ({ ...prev, country: value }))}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select your country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* VIP Photo Upload */}
            <div>
              <Label>VIP Lanyard Photo</Label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-purple-300 border-dashed rounded-md hover:border-purple-400 transition-colors">
                <div className="space-y-1 text-center">
                  <Camera className="mx-auto h-12 w-12 text-purple-400" />
                  <div className="flex text-sm text-purple-600">
                    <label
                      htmlFor="vip-photo-upload"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-purple-600 hover:text-purple-500"
                    >
                      <span>Upload your photo</span>
                      <input
                        id="vip-photo-upload"
                        name="vip-photo-upload"
                        type="file"
                        className="sr-only"
                        accept="image/jpeg,image/png"
                        onChange={handlePhotoUpload}
                      />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-purple-500">JPEG, PNG up to 5MB - Professional photo recommended</p>
                  {formData.profilePhoto && (
                    <p className="text-sm text-purple-600 font-medium">
                      ✓ Photo uploaded: {formData.profilePhoto.name}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* VIP Services & Requests */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">VIP Services & Special Requests</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="specialRequests">Special Requests or Requirements</Label>
              <Textarea
                id="specialRequests"
                value={formData.specialRequests}
                onChange={(e) => setFormData((prev) => ({ ...prev, specialRequests: e.target.value }))}
                className="mt-1"
                placeholder="Please let us know about any special arrangements, dietary requirements, transportation needs, or other requests"
                rows={4}
              />
            </div>

            {/* VIP Car Sticker Information */}
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="flex items-center mb-3">
                <Download className="w-5 h-5 text-purple-600 mr-2" />
                <h4 className="font-semibold text-purple-900">VIP Car Sticker</h4>
              </div>
              <p className="text-purple-800 text-sm mb-3">
                Upon payment confirmation, you'll receive a downloadable VIP car sticker for easy identification and
                parking privileges at the venue.
              </p>
              <div className="text-xs text-purple-700">
                <p>• Priority parking access</p>
                <p>• Easy venue identification</p>
                <p>• Security clearance assistance</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Accessibility Requirements */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">VIP Accessibility & Concierge Services</CardTitle>
            <p className="text-gray-600">Select any accessibility needs or concierge services you require</p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {accessibilityOptions.map((option) => (
                <div key={option} className="flex items-center space-x-3">
                  <Checkbox
                    id={option}
                    checked={formData.accessibilityNeeds.includes(option)}
                    onCheckedChange={(checked) => handleAccessibilityChange(option, checked as boolean)}
                  />
                  <Label htmlFor={option} className="text-sm cursor-pointer">
                    {option}
                  </Label>
                </div>
              ))}
            </div>

            <div>
              <Label htmlFor="otherAccessibility">Other Services or Accessibility Needs</Label>
              <Textarea
                id="otherAccessibility"
                value={formData.otherAccessibility}
                onChange={(e) => setFormData((prev) => ({ ...prev, otherAccessibility: e.target.value }))}
                className="mt-1"
                placeholder="Please describe any other accessibility requirements or VIP services you need"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Terms and Conditions */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-start space-x-3">
              <Checkbox
                id="terms"
                checked={formData.agreedToTerms}
                onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, agreedToTerms: checked as boolean }))}
              />
              <div className="text-sm">
                <label htmlFor="terms" className="cursor-pointer">
                  I agree to the{" "}
                  <a href="/vip-terms" className="text-green-600 hover:underline" target="_blank" rel="noreferrer">
                    VIP Terms & Conditions
                  </a>
                  ,{" "}
                  <a href="/privacy" className="text-green-600 hover:underline" target="_blank" rel="noreferrer">
                    Privacy Policy
                  </a>
                  , and understand that VIP status may require verification
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* VIP Package Summary */}
        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-purple-800 mb-2">VIP Delegate Package Summary</h3>
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium text-purple-900">VIP Delegate Experience</p>
                <p className="text-sm text-purple-700">Premium 3-day access with exclusive amenities</p>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-purple-700">KES 30,000</p>
                <p className="text-sm text-purple-600">Includes gala dinner & VIP services</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Back
          </Button>
          <Button
            type="submit"
            disabled={!formData.agreedToTerms}
            className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 disabled:opacity-50"
          >
            Continue to Review
          </Button>
        </div>
      </form>
    </div>
  )
}
