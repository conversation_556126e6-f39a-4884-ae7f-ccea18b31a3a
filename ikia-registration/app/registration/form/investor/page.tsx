"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { ArrowLeft, TrendingUp, Camera, Lightbulb } from "lucide-react"

const investmentSectors = [
  "Traditional Foods & Nutrition",
  "Local Remedies & Traditional Medicine",
  "Musicology & Cultural Arts",
  "Cultural Tourism & Heritage",
  "Indigenous Technologies & Innovations",
  "Sui Generis Intellectual Property Systems",
]

const countries = [
  "Kenya",
  "Uganda",
  "Tanzania",
  "Rwanda",
  "Burundi",
  "South Sudan",
  "Ethiopia",
  "Somalia",
  "United States",
  "United Kingdom",
  "Other",
]

const accessibilityOptions = [
  "Wheelchair accessibility",
  "Sign language interpretation",
  "Large print materials",
  "Audio assistance",
  "Dietary accommodations",
  "Other accessibility needs",
]

const packageOptions = [
  {
    id: "3day",
    name: "3-Day Investment Discovery",
    price: 15000,
    description: "Full conference access with investment focus sessions",
  },
  {
    id: "daily",
    name: "Daily Investment Discovery",
    price: 8000,
    description: "Single day access with investment opportunities",
  },
  {
    id: "student3day",
    name: "3-Day Student Investment",
    price: 1000,
    description: "Student rate with investment learning focus",
  },
  {
    id: "studentdaily",
    name: "Daily Student Investment",
    price: 500,
    description: "Student daily rate with investment sessions",
  },
]

export default function InvestorRegistration() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    // Personal Details
    fullName: "",
    email: "",
    phone: "",
    jobTitle: "",
    organizationType: "", // individual, company, institution, other
    organizationName: "",
    country: "",
    // Package Selection
    selectedPackage: "",
    // Investment Interests
    investmentSectors: [] as string[],
    otherSector: "",
    investmentExperience: "",
    investmentGoals: "",
    // Accessibility
    accessibilityNeeds: [] as string[],
    otherAccessibility: "",
    // Agreement
    agreedToTerms: false,
    // Photo
    profilePhoto: null as File | null,
  })

  const handleSectorChange = (sector: string, checked: boolean) => {
    if (checked) {
      setFormData((prev) => ({
        ...prev,
        investmentSectors: [...prev.investmentSectors, sector],
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        investmentSectors: prev.investmentSectors.filter((item) => item !== sector),
      }))
    }
  }

  const handleAccessibilityChange = (need: string, checked: boolean) => {
    if (checked) {
      setFormData((prev) => ({
        ...prev,
        accessibilityNeeds: [...prev.accessibilityNeeds, need],
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        accessibilityNeeds: prev.accessibilityNeeds.filter((item) => item !== need),
      }))
    }
  }

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setFormData((prev) => ({ ...prev, profilePhoto: file }))
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    localStorage.setItem("registrationData", JSON.stringify({ type: "investor", ...formData }))
    router.push("/registration/review")
  }

  const selectedPackage = packageOptions.find((pkg) => pkg.id === formData.selectedPackage)
  const isStudentPackage = formData.selectedPackage?.includes("student")

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center mb-8">
        <Button variant="ghost" onClick={() => router.back()} className="mr-4">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <div className="flex items-center">
          <div className="w-10 h-10 gradient-accent rounded-lg flex items-center justify-center mr-3">
            <TrendingUp className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Investment Discovery Registration</h1>
            <p className="text-gray-600">Explore Indigenous Knowledge investment opportunities</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Investment Discovery Notice */}
        <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
          <CardContent className="p-6">
            <div className="flex items-center mb-4">
              <Lightbulb className="w-8 h-8 text-yellow-600 mr-3" />
              <h3 className="text-xl font-semibold text-yellow-900">Investment Discovery Experience</h3>
            </div>
            <p className="text-yellow-800 mb-4">
              Join as a delegate with special focus on investment opportunities in Indigenous Knowledge Intellectual
              Assets. This registration includes all standard delegate benefits plus exclusive access to investment
              showcases and networking.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-yellow-700">
              <div className="flex items-center">
                <TrendingUp className="w-4 h-4 mr-2" />
                Investment opportunity presentations
              </div>
              <div className="flex items-center">
                <TrendingUp className="w-4 h-4 mr-2" />
                Networking with IKIA holders
              </div>
              <div className="flex items-center">
                <TrendingUp className="w-4 h-4 mr-2" />
                Market intelligence sessions
              </div>
              <div className="flex items-center">
                <TrendingUp className="w-4 h-4 mr-2" />
                Due diligence support
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Package Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Select Your Package</CardTitle>
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={formData.selectedPackage}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, selectedPackage: value }))}
              className="space-y-4"
            >
              {packageOptions.map((pkg) => (
                <div key={pkg.id} className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-gray-50">
                  <RadioGroupItem value={pkg.id} id={pkg.id} className="mt-1" />
                  <div className="flex-1">
                    <label htmlFor={pkg.id} className="cursor-pointer">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-gray-900">{pkg.name}</h4>
                        <span className="text-lg font-bold text-green-600">KES {pkg.price.toLocaleString()}</span>
                      </div>
                      <p className="text-sm text-gray-600">{pkg.description}</p>
                    </label>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Personal Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="fullName">Full Name *</Label>
                <Input
                  id="fullName"
                  value={formData.fullName}
                  onChange={(e) => setFormData((prev) => ({ ...prev, fullName: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData((prev) => ({ ...prev, phone: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="jobTitle">Job Title/Position *</Label>
                <Input
                  id="jobTitle"
                  value={formData.jobTitle}
                  onChange={(e) => setFormData((prev) => ({ ...prev, jobTitle: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="organizationType">Organization Type *</Label>
                <Select onValueChange={(value) => setFormData((prev) => ({ ...prev, organizationType: value }))}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select organization type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="individual">Individual</SelectItem>
                    <SelectItem value="company">Company/Business</SelectItem>
                    <SelectItem value="institution">Institution/Organization</SelectItem>
                    <SelectItem value="government">Government Agency</SelectItem>
                    <SelectItem value="ngo">NGO/Non-Profit</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="organizationName">Organization/Company Name</Label>
                <Input
                  id="organizationName"
                  value={formData.organizationName}
                  onChange={(e) => setFormData((prev) => ({ ...prev, organizationName: e.target.value }))}
                  className="mt-1"
                  placeholder="Enter organization name (if applicable)"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="country">Country *</Label>
              <Select onValueChange={(value) => setFormData((prev) => ({ ...prev, country: value }))}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select your country" />
                </SelectTrigger>
                <SelectContent>
                  {countries.map((country) => (
                    <SelectItem key={country} value={country}>
                      {country}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Student ID Upload for Student Packages */}
            {isStudentPackage && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <Label className="text-blue-900 font-semibold">Student ID Verification *</Label>
                <p className="text-blue-700 text-sm mb-3">
                  Please upload a clear photo of your valid student ID for verification
                </p>
                <div className="flex justify-center px-6 pt-5 pb-6 border-2 border-blue-300 border-dashed rounded-md">
                  <div className="space-y-1 text-center">
                    <Camera className="mx-auto h-12 w-12 text-blue-400" />
                    <div className="flex text-sm text-blue-600">
                      <label
                        htmlFor="student-id-upload"
                        className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500"
                      >
                        <span>Upload Student ID</span>
                        <input
                          id="student-id-upload"
                          name="student-id-upload"
                          type="file"
                          className="sr-only"
                          accept="image/*"
                          required
                        />
                      </label>
                    </div>
                    <p className="text-xs text-blue-500">JPEG, PNG up to 5MB</p>
                  </div>
                </div>
              </div>
            )}

            {/* Photo Upload */}
            <div>
              <Label>Profile Photo for Lanyard</Label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-green-400 transition-colors">
                <div className="space-y-1 text-center">
                  <Camera className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600">
                    <label
                      htmlFor="photo-upload"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500"
                    >
                      <span>Upload your photo</span>
                      <input
                        id="photo-upload"
                        name="photo-upload"
                        type="file"
                        className="sr-only"
                        accept="image/jpeg,image/png"
                        onChange={handlePhotoUpload}
                      />
                    </label>
                  </div>
                  <p className="text-xs text-gray-500">JPEG, PNG up to 5MB</p>
                  {formData.profilePhoto && (
                    <p className="text-sm text-green-600 font-medium">✓ Photo uploaded: {formData.profilePhoto.name}</p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Investment Interests */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Investment Interests</CardTitle>
            <p className="text-gray-600">
              Select the sectors you're interested in exploring for investment opportunities
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label className="text-base font-medium">Investment Sectors of Interest (select multiple) *</Label>
              <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                {investmentSectors.map((sector) => (
                  <div key={sector} className="flex items-center space-x-3">
                    <Checkbox
                      id={sector}
                      checked={formData.investmentSectors.includes(sector)}
                      onCheckedChange={(checked) => handleSectorChange(sector, checked as boolean)}
                    />
                    <Label htmlFor={sector} className="text-sm cursor-pointer">
                      {sector}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="otherSector">Other Investment Sectors</Label>
              <Input
                id="otherSector"
                value={formData.otherSector}
                onChange={(e) => setFormData((prev) => ({ ...prev, otherSector: e.target.value }))}
                className="mt-1"
                placeholder="Specify any other sectors of interest"
              />
            </div>

            <div>
              <Label htmlFor="investmentExperience">Investment Experience Level</Label>
              <Select onValueChange={(value) => setFormData((prev) => ({ ...prev, investmentExperience: value }))}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select your investment experience" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="beginner">Beginner - New to investing</SelectItem>
                  <SelectItem value="intermediate">Intermediate - Some investment experience</SelectItem>
                  <SelectItem value="experienced">Experienced - Regular investor</SelectItem>
                  <SelectItem value="professional">Professional - Investment professional</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="investmentGoals">Investment Goals & Interests *</Label>
              <Textarea
                id="investmentGoals"
                value={formData.investmentGoals}
                onChange={(e) => setFormData((prev) => ({ ...prev, investmentGoals: e.target.value }))}
                required
                className="mt-1"
                placeholder="Describe what you hope to learn about Indigenous Knowledge investments, your investment goals, and what types of opportunities interest you"
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        {/* Accessibility Requirements */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Accessibility Requirements</CardTitle>
            <p className="text-gray-600">Please let us know if you need any accessibility accommodations</p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {accessibilityOptions.map((option) => (
                <div key={option} className="flex items-center space-x-3">
                  <Checkbox
                    id={option}
                    checked={formData.accessibilityNeeds.includes(option)}
                    onCheckedChange={(checked) => handleAccessibilityChange(option, checked as boolean)}
                  />
                  <Label htmlFor={option} className="text-sm cursor-pointer">
                    {option}
                  </Label>
                </div>
              ))}
            </div>

            <div>
              <Label htmlFor="otherAccessibility">Other Accessibility Needs</Label>
              <Textarea
                id="otherAccessibility"
                value={formData.otherAccessibility}
                onChange={(e) => setFormData((prev) => ({ ...prev, otherAccessibility: e.target.value }))}
                className="mt-1"
                placeholder="Please describe any other accessibility requirements"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Terms and Conditions */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-start space-x-3">
              <Checkbox
                id="terms"
                checked={formData.agreedToTerms}
                onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, agreedToTerms: checked as boolean }))}
              />
              <div className="text-sm">
                <label htmlFor="terms" className="cursor-pointer">
                  I agree to the{" "}
                  <a href="/terms" className="text-green-600 hover:underline" target="_blank" rel="noreferrer">
                    Terms & Conditions
                  </a>
                  ,{" "}
                  <a href="/privacy" className="text-green-600 hover:underline" target="_blank" rel="noreferrer">
                    Privacy Policy
                  </a>
                  , and understand this is a delegate registration with investment focus
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Package Summary */}
        {selectedPackage && (
          <Card className="bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-yellow-800 mb-2">Selected Package Summary</h3>
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium text-yellow-900">{selectedPackage.name}</p>
                  <p className="text-sm text-yellow-700">{selectedPackage.description}</p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-yellow-700">KES {selectedPackage.price.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Submit Button */}
        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Back
          </Button>
          <Button
            type="submit"
            disabled={!formData.agreedToTerms || !formData.selectedPackage || formData.investmentSectors.length === 0}
            className="btn-accent disabled:opacity-50"
          >
            Continue to Review
          </Button>
        </div>
      </form>
    </div>
  )
}
