"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { ArrowLeft, Users, Upload, Camera } from "lucide-react"

const thematicAreas = [
  "Traditional Foods & Nutrition",
  "Local Remedies & Traditional Medicine",
  "Musicology & Cultural Arts",
  "Cultural Tourism & Heritage",
  "Indigenous Technologies & Innovations",
  "Sui Generis Intellectual Property Systems",
]

const countries = [
  "Kenya",
  "Uganda",
  "Tanzania",
  "Rwanda",
  "Burundi",
  "South Sudan",
  "Ethiopia",
  "Somalia",
  "United States",
  "United Kingdom",
  "Other",
]

const accessibilityOptions = [
  "Wheelchair accessibility",
  "Sign language interpretation",
  "Large print materials",
  "Audio assistance",
  "Dietary accommodations",
  "Other accessibility needs",
]

const packageOptions = [
  {
    id: "3day",
    name: "3-Day Delegate Ticket",
    price: 15000,
    description: "Full access to all sessions, conference materials, tea breaks, lunch, and gift pack",
  },
  {
    id: "daily",
    name: "Daily Delegate Ticket",
    price: 8000,
    description: "Access to all conference sessions, site visits, exhibition area for 1 day",
  },
  {
    id: "student3day",
    name: "3-Day Student Ticket",
    price: 1000,
    description: "Student rate - requires valid student ID for accreditation",
  },
  {
    id: "studentdaily",
    name: "Daily Student Ticket",
    price: 500,
    description: "Student daily rate - requires valid student ID for accreditation",
  },
]

export default function GuestRegistration() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    // Personal Details
    fullName: "",
    email: "",
    phone: "",
    jobTitle: "",
    organization: "",
    country: "",
    // Conference Interests
    selectedPackage: "",
    thematicInterests: [] as string[],
    otherInterest: "",
    // Accessibility
    accessibilityNeeds: [] as string[],
    otherAccessibility: "",
    // Agreement
    agreedToTerms: false,
    // Photo for lanyard
    profilePhoto: null as File | null,
  })

  const handleThematicInterestChange = (interest: string, checked: boolean) => {
    if (checked) {
      setFormData((prev) => ({
        ...prev,
        thematicInterests: [...prev.thematicInterests, interest],
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        thematicInterests: prev.thematicInterests.filter((item) => item !== interest),
      }))
    }
  }

  const handleAccessibilityChange = (need: string, checked: boolean) => {
    if (checked) {
      setFormData((prev) => ({
        ...prev,
        accessibilityNeeds: [...prev.accessibilityNeeds, need],
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        accessibilityNeeds: prev.accessibilityNeeds.filter((item) => item !== need),
      }))
    }
  }

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setFormData((prev) => ({ ...prev, profilePhoto: file }))
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    localStorage.setItem("registrationData", JSON.stringify({ type: "guest", ...formData }))
    router.push("/registration/review")
  }

  const selectedPackage = packageOptions.find((pkg) => pkg.id === formData.selectedPackage)
  const isStudentPackage = formData.selectedPackage?.includes("student")

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center mb-8">
        <Button variant="ghost" onClick={() => router.back()} className="mr-4">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <div className="flex items-center">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
            <Users className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Conference Delegate Registration</h1>
            <p className="text-gray-600">Join the Indigenous Knowledge innovation community</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Package Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Select Your Package</CardTitle>
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={formData.selectedPackage}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, selectedPackage: value }))}
              className="space-y-4"
            >
              {packageOptions.map((pkg) => (
                <div key={pkg.id} className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-gray-50">
                  <RadioGroupItem value={pkg.id} id={pkg.id} className="mt-1" />
                  <div className="flex-1">
                    <label htmlFor={pkg.id} className="cursor-pointer">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-gray-900">{pkg.name}</h4>
                        <span className="text-lg font-bold text-green-600">KES {pkg.price.toLocaleString()}</span>
                      </div>
                      <p className="text-sm text-gray-600">{pkg.description}</p>
                    </label>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Personal Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="fullName">Full Name *</Label>
                <Input
                  id="fullName"
                  value={formData.fullName}
                  onChange={(e) => setFormData((prev) => ({ ...prev, fullName: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData((prev) => ({ ...prev, phone: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="jobTitle">Job Title/Position *</Label>
                <Input
                  id="jobTitle"
                  value={formData.jobTitle}
                  onChange={(e) => setFormData((prev) => ({ ...prev, jobTitle: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="organization">Organization/Institution *</Label>
                <Input
                  id="organization"
                  value={formData.organization}
                  onChange={(e) => setFormData((prev) => ({ ...prev, organization: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="country">Country *</Label>
                <Select onValueChange={(value) => setFormData((prev) => ({ ...prev, country: value }))}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select your country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Student ID Upload for Student Packages */}
            {isStudentPackage && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <Label className="text-blue-900 font-semibold">Student ID Verification *</Label>
                <p className="text-blue-700 text-sm mb-3">
                  Please upload a clear photo of your valid student ID for verification
                </p>
                <div className="flex justify-center px-6 pt-5 pb-6 border-2 border-blue-300 border-dashed rounded-md">
                  <div className="space-y-1 text-center">
                    <Upload className="mx-auto h-12 w-12 text-blue-400" />
                    <div className="flex text-sm text-blue-600">
                      <label
                        htmlFor="student-id-upload"
                        className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500"
                      >
                        <span>Upload Student ID</span>
                        <input
                          id="student-id-upload"
                          name="student-id-upload"
                          type="file"
                          className="sr-only"
                          accept="image/*"
                          required
                        />
                      </label>
                    </div>
                    <p className="text-xs text-blue-500">JPEG, PNG up to 5MB</p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Conference Track Interests */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Conference Track Interests</CardTitle>
            <p className="text-gray-600">Select the thematic areas you're most interested in (select multiple)</p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {thematicAreas.map((area) => (
                <div key={area} className="flex items-center space-x-3">
                  <Checkbox
                    id={area}
                    checked={formData.thematicInterests.includes(area)}
                    onCheckedChange={(checked) => handleThematicInterestChange(area, checked as boolean)}
                  />
                  <Label htmlFor={area} className="text-sm cursor-pointer">
                    {area}
                  </Label>
                </div>
              ))}
            </div>

            <div>
              <Label htmlFor="otherInterest">Other Interest (please specify)</Label>
              <Input
                id="otherInterest"
                value={formData.otherInterest}
                onChange={(e) => setFormData((prev) => ({ ...prev, otherInterest: e.target.value }))}
                className="mt-1"
                placeholder="Specify any other areas of interest"
              />
            </div>
          </CardContent>
        </Card>

        {/* Lanyard Photo Upload */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Lanyard Photo</CardTitle>
            <p className="text-gray-600">
              Upload your photo for the conference lanyard (with QR code for accessibility)
            </p>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-green-400 transition-colors">
              <div className="space-y-1 text-center">
                <Camera className="mx-auto h-12 w-12 text-gray-400" />
                <div className="flex text-sm text-gray-600">
                  <label
                    htmlFor="photo-upload"
                    className="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500"
                  >
                    <span>Upload your photo</span>
                    <input
                      id="photo-upload"
                      name="photo-upload"
                      type="file"
                      className="sr-only"
                      accept="image/jpeg,image/png"
                      onChange={handlePhotoUpload}
                    />
                  </label>
                  <p className="pl-1">or drag and drop</p>
                </div>
                <p className="text-xs text-gray-500">JPEG, PNG up to 5MB - Professional headshot recommended</p>
                {formData.profilePhoto && (
                  <p className="text-sm text-green-600 font-medium">✓ Photo uploaded: {formData.profilePhoto.name}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Accessibility Requirements */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Accessibility Requirements</CardTitle>
            <p className="text-gray-600">Please let us know if you need any accessibility accommodations</p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {accessibilityOptions.map((option) => (
                <div key={option} className="flex items-center space-x-3">
                  <Checkbox
                    id={option}
                    checked={formData.accessibilityNeeds.includes(option)}
                    onCheckedChange={(checked) => handleAccessibilityChange(option, checked as boolean)}
                  />
                  <Label htmlFor={option} className="text-sm cursor-pointer">
                    {option}
                  </Label>
                </div>
              ))}
            </div>

            <div>
              <Label htmlFor="otherAccessibility">Other Accessibility Needs</Label>
              <Textarea
                id="otherAccessibility"
                value={formData.otherAccessibility}
                onChange={(e) => setFormData((prev) => ({ ...prev, otherAccessibility: e.target.value }))}
                className="mt-1"
                placeholder="Please describe any other accessibility requirements"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Terms and Conditions */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-start space-x-3">
              <Checkbox
                id="terms"
                checked={formData.agreedToTerms}
                onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, agreedToTerms: checked as boolean }))}
              />
              <div className="text-sm">
                <label htmlFor="terms" className="cursor-pointer">
                  I agree to the{" "}
                  <a href="/terms" className="text-green-600 hover:underline" target="_blank" rel="noreferrer">
                    Terms & Conditions
                  </a>{" "}
                  and{" "}
                  <a href="/privacy" className="text-green-600 hover:underline" target="_blank" rel="noreferrer">
                    Privacy Policy
                  </a>
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Package Summary */}
        {selectedPackage && (
          <Card className="bg-green-50 border-green-200">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-green-800 mb-2">Selected Package Summary</h3>
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium text-green-900">{selectedPackage.name}</p>
                  <p className="text-sm text-green-700">{selectedPackage.description}</p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-green-700">KES {selectedPackage.price.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Submit Button */}
        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Back
          </Button>
          <Button
            type="submit"
            disabled={!formData.agreedToTerms || !formData.selectedPackage}
            className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50"
          >
            Continue to Review
          </Button>
        </div>
      </form>
    </div>
  )
}
