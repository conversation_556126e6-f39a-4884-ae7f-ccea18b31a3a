"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Store, Camera, FileText, Plus, Trash2 } from "lucide-react"

const thematicAreas = [
  "Traditional Foods & Nutrition",
  "Local Remedies & Traditional Medicine",
  "Musicology & Cultural Arts",
  "Cultural Tourism & Heritage",
  "Indigenous Technologies & Innovations",
  "Sui Generis Intellectual Property Systems",
]

const countries = [
  "Kenya",
  "Uganda",
  "Tanzania",
  "Rwanda",
  "Burundi",
  "South Sudan",
  "Ethiopia",
  "Somalia",
  "United States",
  "United Kingdom",
  "Other",
]

const accessibilityOptions = [
  "Wheelchair accessibility",
  "Sign language interpretation",
  "Large print materials",
  "Audio assistance",
  "Dietary accommodations",
  "Other accessibility needs",
]

interface TeamMember {
  id: string
  fullName: string
  email: string
  phone: string
  role: string
  photo: File | null
}

export default function ExhibitorRegistration() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    // Contact Details
    fullName: "",
    email: "",
    phone: "",
    jobTitle: "",
    // Company Details
    companyName: "",
    website: "",
    description: "",
    primaryIndustry: "",
    otherIndustry: "",
    country: "",
    // Exhibition Details
    boothSize: "standard",
    products: "",
    targetAudience: "",
    // Accessibility
    accessibilityNeeds: [] as string[],
    otherAccessibility: "",
    // Agreement
    agreedToTerms: false,
    // Registration Type
    registrationType: "company", // company or individual
    // Team Members
    teamMembers: [] as TeamMember[],
  })

  const [showExhibitorGuide, setShowExhibitorGuide] = useState(false)

  const handleAccessibilityChange = (need: string, checked: boolean) => {
    if (checked) {
      setFormData((prev) => ({
        ...prev,
        accessibilityNeeds: [...prev.accessibilityNeeds, need],
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        accessibilityNeeds: prev.accessibilityNeeds.filter((item) => item !== need),
      }))
    }
  }

  const addTeamMember = () => {
    const newMember: TeamMember = {
      id: Date.now().toString(),
      fullName: "",
      email: "",
      phone: "",
      role: "",
      photo: null,
    }
    setFormData((prev) => ({
      ...prev,
      teamMembers: [...prev.teamMembers, newMember],
    }))
  }

  const removeTeamMember = (id: string) => {
    setFormData((prev) => ({
      ...prev,
      teamMembers: prev.teamMembers.filter((member) => member.id !== id),
    }))
  }

  const updateTeamMember = (id: string, field: keyof TeamMember, value: any) => {
    setFormData((prev) => ({
      ...prev,
      teamMembers: prev.teamMembers.map((member) => (member.id === id ? { ...member, [field]: value } : member)),
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    localStorage.setItem("registrationData", JSON.stringify({ type: "exhibitor", ...formData }))
    router.push("/registration/review")
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center mb-8">
        <Button variant="ghost" onClick={() => router.back()} className="mr-4">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <div className="flex items-center">
          <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
            <Store className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Exhibitor Registration</h1>
            <p className="text-gray-600">Showcase your Indigenous Knowledge innovations</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Registration Type */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Registration Type</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  formData.registrationType === "company"
                    ? "border-green-500 bg-green-50"
                    : "border-gray-200 hover:border-green-300"
                }`}
                onClick={() => setFormData((prev) => ({ ...prev, registrationType: "company" }))}
              >
                <h4 className="font-semibold text-gray-900">Company Registration</h4>
                <p className="text-sm text-gray-600">Register as a company or organization</p>
              </div>
              <div
                className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  formData.registrationType === "individual"
                    ? "border-green-500 bg-green-50"
                    : "border-gray-200 hover:border-green-300"
                }`}
                onClick={() => setFormData((prev) => ({ ...prev, registrationType: "individual" }))}
              >
                <h4 className="font-semibold text-gray-900">Individual Registration</h4>
                <p className="text-sm text-gray-600">Register as an individual exhibitor</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Details */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Primary Contact Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="fullName">Full Name *</Label>
                <Input
                  id="fullName"
                  value={formData.fullName}
                  onChange={(e) => setFormData((prev) => ({ ...prev, fullName: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData((prev) => ({ ...prev, email: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData((prev) => ({ ...prev, phone: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="jobTitle">Job Title/Position *</Label>
                <Input
                  id="jobTitle"
                  value={formData.jobTitle}
                  onChange={(e) => setFormData((prev) => ({ ...prev, jobTitle: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
            </div>

            {/* Photo Upload for Primary Contact */}
            <div>
              <Label>Profile Photo for Lanyard</Label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-green-400 transition-colors">
                <div className="space-y-1 text-center">
                  <Camera className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600">
                    <label
                      htmlFor="primary-photo-upload"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500"
                    >
                      <span>Upload your photo</span>
                      <input
                        id="primary-photo-upload"
                        name="primary-photo-upload"
                        type="file"
                        className="sr-only"
                        accept="image/jpeg,image/png"
                      />
                    </label>
                  </div>
                  <p className="text-xs text-gray-500">JPEG, PNG up to 5MB</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Company Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">
              {formData.registrationType === "company" ? "Company Information" : "Business Information"}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="companyName">
                  {formData.registrationType === "company" ? "Company Name" : "Business Name"} *
                </Label>
                <Input
                  id="companyName"
                  value={formData.companyName}
                  onChange={(e) => setFormData((prev) => ({ ...prev, companyName: e.target.value }))}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="website">Website (Optional)</Label>
                <Input
                  id="website"
                  type="url"
                  value={formData.website}
                  onChange={(e) => setFormData((prev) => ({ ...prev, website: e.target.value }))}
                  className="mt-1"
                  placeholder="https://www.example.com"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Business Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                required
                className="mt-1"
                placeholder="Describe your business and the Indigenous Knowledge assets you'll be showcasing"
                rows={4}
                maxLength={1000}
              />
              <p className="text-sm text-gray-500 mt-1">{formData.description.length}/1000 characters</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="primaryIndustry">Primary Industry/Thematic Area *</Label>
                <Select onValueChange={(value) => setFormData((prev) => ({ ...prev, primaryIndustry: value }))}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select primary thematic area" />
                  </SelectTrigger>
                  <SelectContent>
                    {thematicAreas.map((area) => (
                      <SelectItem key={area} value={area}>
                        {area}
                      </SelectItem>
                    ))}
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="country">Country *</Label>
                <Select onValueChange={(value) => setFormData((prev) => ({ ...prev, country: value }))}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {formData.primaryIndustry === "other" && (
              <div>
                <Label htmlFor="otherIndustry">Please specify other industry *</Label>
                <Input
                  id="otherIndustry"
                  value={formData.otherIndustry}
                  onChange={(e) => setFormData((prev) => ({ ...prev, otherIndustry: e.target.value }))}
                  required
                  className="mt-1"
                  placeholder="Specify your industry/thematic area"
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Exhibition Details */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Exhibition Package</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold text-blue-900">Standard Exhibitor Package</h4>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setShowExhibitorGuide(!showExhibitorGuide)}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  View Exhibitor Guide
                </Button>
              </div>
              <div className="text-blue-800 space-y-2">
                <p className="font-medium">KES 70,000 - Includes:</p>
                <ul className="text-sm space-y-1 ml-4">
                  <li>• Standard 3x3m booth space</li>
                  <li>• Access to dealrooms</li>
                  <li>• 2 delegate passes for 3 days</li>
                  <li>• Access to all conference sessions</li>
                  <li>• Site visits and networking sessions</li>
                  <li>• Gift hamper and dinner inclusion</li>
                  <li>• Media coverage opportunities</li>
                  <li>• Certificate of participation</li>
                </ul>
              </div>
            </div>

            {showExhibitorGuide && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h5 className="font-semibold text-gray-900 mb-3">Exhibitor Guide Details</h5>
                <div className="text-sm text-gray-700 space-y-2">
                  <p>
                    <strong>Booth Setup:</strong> Setup begins November 17th, 2pm-6pm
                  </p>
                  <p>
                    <strong>Booth Specifications:</strong> 3m x 3m space with basic furniture
                  </p>
                  <p>
                    <strong>Included:</strong> Table, 2 chairs, power outlet, basic lighting
                  </p>
                  <p>
                    <strong>Additional Services:</strong> Available for extra cost (AV equipment, additional furniture)
                  </p>
                  <p>
                    <strong>Breakdown:</strong> November 21st, 6pm-8pm
                  </p>
                </div>
              </div>
            )}

            <div>
              <Label htmlFor="products">Products/Services to Showcase *</Label>
              <Textarea
                id="products"
                value={formData.products}
                onChange={(e) => setFormData((prev) => ({ ...prev, products: e.target.value }))}
                required
                className="mt-1"
                placeholder="Describe the Indigenous Knowledge products, services, or innovations you'll be showcasing"
                rows={4}
              />
            </div>

            <div>
              <Label htmlFor="targetAudience">Target Audience *</Label>
              <Textarea
                id="targetAudience"
                value={formData.targetAudience}
                onChange={(e) => setFormData((prev) => ({ ...prev, targetAudience: e.target.value }))}
                required
                className="mt-1"
                placeholder="Who are you hoping to connect with at the conference? (investors, buyers, partners, etc.)"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Team Members */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700 flex items-center justify-between">
              Team Members & Representatives
              <Button type="button" variant="outline" size="sm" onClick={addTeamMember}>
                <Plus className="w-4 h-4 mr-2" />
                Add Team Member
              </Button>
            </CardTitle>
            <p className="text-gray-600">Add additional team members who will represent your organization</p>
          </CardHeader>
          <CardContent className="space-y-6">
            {formData.teamMembers.map((member, index) => (
              <div key={member.id} className="p-4 border rounded-lg bg-gray-50">
                <div className="flex items-center justify-between mb-4">
                  <h5 className="font-medium text-gray-900">Team Member {index + 1}</h5>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeTeamMember(member.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <Label>Full Name *</Label>
                    <Input
                      value={member.fullName}
                      onChange={(e) => updateTeamMember(member.id, "fullName", e.target.value)}
                      required
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label>Email *</Label>
                    <Input
                      type="email"
                      value={member.email}
                      onChange={(e) => updateTeamMember(member.id, "email", e.target.value)}
                      required
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label>Phone *</Label>
                    <Input
                      value={member.phone}
                      onChange={(e) => updateTeamMember(member.id, "phone", e.target.value)}
                      required
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label>Role/Position *</Label>
                    <Input
                      value={member.role}
                      onChange={(e) => updateTeamMember(member.id, "role", e.target.value)}
                      required
                      className="mt-1"
                    />
                  </div>
                </div>

                {/* Photo Upload for Team Member */}
                <div>
                  <Label>Profile Photo for Lanyard</Label>
                  <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-green-400 transition-colors">
                    <div className="space-y-1 text-center">
                      <Camera className="mx-auto h-8 w-8 text-gray-400" />
                      <div className="flex text-sm text-gray-600">
                        <label
                          htmlFor={`team-photo-${member.id}`}
                          className="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500"
                        >
                          <span>Upload photo</span>
                          <input
                            id={`team-photo-${member.id}`}
                            type="file"
                            className="sr-only"
                            accept="image/jpeg,image/png"
                            onChange={(e) => {
                              const file = e.target.files?.[0]
                              if (file) updateTeamMember(member.id, "photo", file)
                            }}
                          />
                        </label>
                      </div>
                      <p className="text-xs text-gray-500">JPEG, PNG up to 5MB</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Accessibility Requirements */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Accessibility Requirements</CardTitle>
            <p className="text-gray-600">Please let us know if you need any accessibility accommodations</p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {accessibilityOptions.map((option) => (
                <div key={option} className="flex items-center space-x-3">
                  <Checkbox
                    id={option}
                    checked={formData.accessibilityNeeds.includes(option)}
                    onCheckedChange={(checked) => handleAccessibilityChange(option, checked as boolean)}
                  />
                  <Label htmlFor={option} className="text-sm cursor-pointer">
                    {option}
                  </Label>
                </div>
              ))}
            </div>

            <div>
              <Label htmlFor="otherAccessibility">Other Accessibility Needs</Label>
              <Textarea
                id="otherAccessibility"
                value={formData.otherAccessibility}
                onChange={(e) => setFormData((prev) => ({ ...prev, otherAccessibility: e.target.value }))}
                className="mt-1"
                placeholder="Please describe any other accessibility requirements"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Terms and Conditions */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-start space-x-3">
              <Checkbox
                id="terms"
                checked={formData.agreedToTerms}
                onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, agreedToTerms: checked as boolean }))}
              />
              <div className="text-sm">
                <label htmlFor="terms" className="cursor-pointer">
                  I agree to the{" "}
                  <a
                    href="/exhibitor-terms"
                    className="text-green-600 hover:underline"
                    target="_blank"
                    rel="noreferrer"
                  >
                    Exhibitor Terms & Conditions
                  </a>
                  ,{" "}
                  <a href="/privacy" className="text-green-600 hover:underline" target="_blank" rel="noreferrer">
                    Privacy Policy
                  </a>
                  , and{" "}
                  <a
                    href="/exhibitor-guidelines"
                    className="text-green-600 hover:underline"
                    target="_blank"
                    rel="noreferrer"
                  >
                    Exhibition Guidelines
                  </a>
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Package Summary */}
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-green-800 mb-2">Exhibition Package Summary</h3>
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium text-green-900">Standard Exhibitor Package</p>
                <p className="text-sm text-green-700">3x3m booth + 2 delegate passes + full conference access</p>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-green-700">KES 70,000</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Back
          </Button>
          <Button
            type="submit"
            disabled={!formData.agreedToTerms}
            className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50"
          >
            Continue to Review
          </Button>
        </div>
      </form>
    </div>
  )
}
