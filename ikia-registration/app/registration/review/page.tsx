"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Edit, CheckCircle, CreditCard, Smartphone, Building, Shield, Info, Clock } from "lucide-react"

// Kenyan Payment Methods
const paymentMethods = [
  {
    id: "mpesa",
    name: "M-Pesa",
    description: "Pay using your M-Pesa mobile money account",
    icon: Smartphone,
    fees: "2.5%",
    processingTime: "Instant",
    popular: true,
  },
  {
    id: "airtel_money",
    name: "Airtel Money",
    description: "Pay using your Airtel Money mobile wallet",
    icon: Smartphone,
    fees: "2.5%",
    processingTime: "Instant",
    popular: false,
  },
  {
    id: "tkash",
    name: "T-<PERSON><PERSON>",
    description: "Pay using your T-Kash mobile money service",
    icon: Smartphone,
    fees: "2.5%",
    processingTime: "Instant",
    popular: false,
  },
  {
    id: "bank_transfer",
    name: "Bank Transfer",
    description: "Direct bank transfer to IKIA Conference account",
    icon: Building,
    fees: "Free",
    processingTime: "1-2 business days",
    popular: false,
  },
  {
    id: "debit_card",
    name: "Debit/Credit Card",
    description: "Pay using Visa, Mastercard, or local bank cards",
    icon: CreditCard,
    fees: "3.5%",
    processingTime: "Instant",
    popular: false,
  },
]

// Registration pricing in KES
const registrationPricing = {
  guest: {
    earlyBird: 15000,
    regular: 20000,
    deadline: "February 15, 2025",
  },
  exhibitor: {
    standard: 75000,
    premium: 150000,
    custom: "Contact us",
  },
  investor: {
    application: 0,
    verification: "Required",
  },
  vip: {
    invitation: 0,
    note: "By invitation only",
  },
  sponsor: {
    bronze: 500000,
    silver: 1000000,
    gold: 2500000,
    platinum: 5000000,
  },
}

export default function ReviewRegistration() {
  const router = useRouter()
  const [registrationData, setRegistrationData] = useState<any>(null)
  const [agreedToTerms, setAgreedToTerms] = useState(false)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("")
  const [paymentDetails, setPaymentDetails] = useState({
    phoneNumber: "",
    cardNumber: "",
    expiryDate: "",
    cvv: "",
    bankAccount: "",
  })
  const [isProcessing, setIsProcessing] = useState(false)

  useEffect(() => {
    const data = localStorage.getItem("registrationData")
    if (data) {
      setRegistrationData(JSON.parse(data))
    } else {
      router.push("/registration")
    }
  }, [router])

  const calculateTotal = () => {
    if (!registrationData) return { baseAmount: 0, processingFee: 0, total: 0 }

    const type = registrationData.type
    let baseAmount = 0

    switch (type) {
      case "guest":
        const packageType = registrationData.selectedPackage
        if (packageType === "3day") baseAmount = 15000
        else if (packageType === "daily") baseAmount = 8000
        else if (packageType === "student3day") baseAmount = 1000
        else if (packageType === "studentdaily") baseAmount = 500
        break
      case "exhibitor":
        baseAmount = 70000 // Standard exhibitor package
        break
      case "investor":
        baseAmount = 0 // Free for investors
        break
      case "vip":
        baseAmount = 30000 // VIP delegate package
        break
      case "sponsor":
        const sponsorLevel = registrationData.sponsorshipLevel
        const sponsorPricing = {
          bronze: 500000,
          silver: 1000000,
          gold: 2500000,
          platinum: 5000000,
        }
        baseAmount = sponsorPricing[sponsorLevel as keyof typeof sponsorPricing] || 0
        break
      default:
        baseAmount = 0
    }

    // Calculate payment processing fees
    const selectedMethod = paymentMethods.find((method) => method.id === selectedPaymentMethod)
    const feePercentage = selectedMethod?.fees === "Free" ? 0 : Number.parseFloat(selectedMethod?.fees || "0") / 100
    const processingFee = baseAmount * feePercentage

    return {
      baseAmount,
      processingFee,
      total: baseAmount + processingFee,
    }
  }

  const handleSubmit = async () => {
    if (agreedToTerms && selectedPaymentMethod) {
      setIsProcessing(true)
      // Simulate payment processing
      setTimeout(() => {
        router.push("/registration/success")
      }, 3000)
    }
  }

  const getUserTypeDisplay = (type: string) => {
    const types: { [key: string]: string } = {
      guest: "Conference Attendee",
      exhibitor: "Trade Fair Exhibitor",
      investor: "Investment Partner",
      vip: "VIP Delegate",
      sponsor: "Conference Sponsor",
    }
    return types[type] || type
  }

  if (!registrationData) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-green-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="body-base text-gray-600">Loading registration data...</p>
        </div>
      </div>
    )
  }

  const pricing = calculateTotal()

  return (
    <div className="max-w-5xl mx-auto">
      <div className="flex items-center mb-12">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mr-6 hover:bg-green-50 transition-colors"
          disabled={isProcessing}
        >
          <ArrowLeft className="w-5 h-5 mr-2" />
          Back
        </Button>

        <div className="flex items-center">
          <div className="w-14 h-14 gradient-primary rounded-2xl flex items-center justify-center mr-4 shadow-lg">
            <CheckCircle className="w-7 h-7 text-white" />
          </div>
          <div>
            <h1 className="heading-2 text-gray-900 mb-2">Review & Confirm Registration</h1>
            <p className="body-base text-gray-600">Please review your information and complete payment</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Registration Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Registration Type */}
          <Card className="card-modern">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="heading-4 text-green-700">Registration Type</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push("/registration")}
                disabled={isProcessing}
                className="text-green-600 hover:text-green-700"
              >
                <Edit className="w-4 h-4 mr-2" />
                Change
              </Button>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <p className="heading-4 text-gray-900">{getUserTypeDisplay(registrationData.type)}</p>
                  <p className="body-small text-gray-600">IKIA 2025 Conference Registration</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Personal Information */}
          <Card className="card-modern">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="heading-4 text-green-700">Personal Information</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/registration/form/${registrationData.type}`)}
                disabled={isProcessing}
                className="text-green-600 hover:text-green-700"
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <p className="body-small text-gray-500 mb-1">Full Name</p>
                  <p className="body-base font-semibold text-gray-900">{registrationData.fullName}</p>
                </div>
                <div>
                  <p className="body-small text-gray-500 mb-1">Email Address</p>
                  <p className="body-base font-semibold text-gray-900">{registrationData.email}</p>
                </div>
                <div>
                  <p className="body-small text-gray-500 mb-1">Phone Number</p>
                  <p className="body-base font-semibold text-gray-900">{registrationData.phone}</p>
                </div>
                {registrationData.country && (
                  <div>
                    <p className="body-small text-gray-500 mb-1">Country</p>
                    <p className="body-base font-semibold text-gray-900">{registrationData.country}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Company/Organization Information */}
          {(registrationData.companyName || registrationData.organization) && (
            <Card className="card-modern">
              <CardHeader>
                <CardTitle className="heading-4 text-green-700">
                  {registrationData.type === "guest" ? "Organization" : "Company Information"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {registrationData.companyName && (
                    <div>
                      <p className="body-small text-gray-500 mb-1">Company Name</p>
                      <p className="body-base font-semibold text-gray-900">{registrationData.companyName}</p>
                    </div>
                  )}
                  {registrationData.organization && (
                    <div>
                      <p className="body-small text-gray-500 mb-1">Organization</p>
                      <p className="body-base font-semibold text-gray-900">{registrationData.organization}</p>
                    </div>
                  )}
                  {registrationData.website && (
                    <div>
                      <p className="body-small text-gray-500 mb-1">Website</p>
                      <p className="body-base font-semibold text-blue-600">{registrationData.website}</p>
                    </div>
                  )}
                  {registrationData.jobTitle && (
                    <div>
                      <p className="body-small text-gray-500 mb-1">Job Title</p>
                      <p className="body-base font-semibold text-gray-900">{registrationData.jobTitle}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Method Selection */}
          <Card className="card-modern">
            <CardHeader>
              <CardTitle className="heading-4 text-green-700 flex items-center">
                <CreditCard className="w-5 h-5 mr-2" />
                Payment Method
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {paymentMethods.map((method) => {
                  const IconComponent = method.icon
                  const isSelected = selectedPaymentMethod === method.id

                  return (
                    <div
                      key={method.id}
                      className={`
                        p-4 border-2 rounded-xl cursor-pointer transition-all duration-300
                        ${
                          isSelected
                            ? "border-green-500 bg-green-50 shadow-lg"
                            : "border-gray-200 hover:border-green-300 hover:bg-gray-50"
                        }
                        ${method.popular ? "ring-2 ring-yellow-400 ring-opacity-50" : ""}
                      `}
                      onClick={() => setSelectedPaymentMethod(method.id)}
                    >
                      {method.popular && (
                        <div className="text-xs font-semibold text-yellow-700 bg-yellow-100 px-2 py-1 rounded-full mb-2 inline-block">
                          Most Popular
                        </div>
                      )}

                      <div className="flex items-start space-x-3">
                        <div
                          className={`
                          w-10 h-10 rounded-lg flex items-center justify-center
                          ${isSelected ? "gradient-primary" : "bg-gray-100"}
                        `}
                        >
                          <IconComponent className={`w-5 h-5 ${isSelected ? "text-white" : "text-gray-600"}`} />
                        </div>

                        <div className="flex-1">
                          <h4 className="body-base font-semibold text-gray-900 mb-1">{method.name}</h4>
                          <p className="body-small text-gray-600 mb-2">{method.description}</p>

                          <div className="flex items-center justify-between text-xs">
                            <span className="text-gray-500">Fee: {method.fees}</span>
                            <span className="text-gray-500 flex items-center">
                              <Clock className="w-3 h-3 mr-1" />
                              {method.processingTime}
                            </span>
                          </div>
                        </div>

                        {isSelected && (
                          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                            <CheckCircle className="w-4 h-4 text-white" />
                          </div>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>

              {/* Payment Details Form */}
              {selectedPaymentMethod && (
                <div className="mt-6 p-6 bg-gray-50 rounded-xl">
                  <h5 className="heading-4 text-gray-900 mb-4">Payment Details</h5>

                  {(selectedPaymentMethod === "mpesa" ||
                    selectedPaymentMethod === "airtel_money" ||
                    selectedPaymentMethod === "tkash") && (
                    <div>
                      <Label htmlFor="phoneNumber" className="body-base font-semibold text-gray-700">
                        Mobile Number *
                      </Label>
                      <Input
                        id="phoneNumber"
                        type="tel"
                        placeholder="0712345678"
                        value={paymentDetails.phoneNumber}
                        onChange={(e) => setPaymentDetails((prev) => ({ ...prev, phoneNumber: e.target.value }))}
                        className="input-modern mt-2"
                        required
                      />
                      <p className="body-small text-gray-500 mt-1">
                        You will receive a payment prompt on your mobile device
                      </p>
                    </div>
                  )}

                  {selectedPaymentMethod === "debit_card" && (
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="cardNumber" className="body-base font-semibold text-gray-700">
                          Card Number *
                        </Label>
                        <Input
                          id="cardNumber"
                          type="text"
                          placeholder="1234 5678 9012 3456"
                          value={paymentDetails.cardNumber}
                          onChange={(e) => setPaymentDetails((prev) => ({ ...prev, cardNumber: e.target.value }))}
                          className="input-modern mt-2"
                          required
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="expiryDate" className="body-base font-semibold text-gray-700">
                            Expiry Date *
                          </Label>
                          <Input
                            id="expiryDate"
                            type="text"
                            placeholder="MM/YY"
                            value={paymentDetails.expiryDate}
                            onChange={(e) => setPaymentDetails((prev) => ({ ...prev, expiryDate: e.target.value }))}
                            className="input-modern mt-2"
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="cvv" className="body-base font-semibold text-gray-700">
                            CVV *
                          </Label>
                          <Input
                            id="cvv"
                            type="text"
                            placeholder="123"
                            value={paymentDetails.cvv}
                            onChange={(e) => setPaymentDetails((prev) => ({ ...prev, cvv: e.target.value }))}
                            className="input-modern mt-2"
                            required
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {selectedPaymentMethod === "bank_transfer" && (
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="flex items-start space-x-3">
                        <Info className="w-5 h-5 text-blue-600 mt-0.5" />
                        <div>
                          <h6 className="body-base font-semibold text-blue-900 mb-2">Bank Transfer Details</h6>
                          <div className="space-y-1 body-small text-blue-800">
                            <p>
                              <strong>Bank:</strong> Kenya Commercial Bank (KCB)
                            </p>
                            <p>
                              <strong>Account Name:</strong> IKIA Conference 2025
                            </p>
                            <p>
                              <strong>Account Number:</strong> **********
                            </p>
                            <p>
                              <strong>Branch:</strong> Nairobi Main Branch
                            </p>
                            <p>
                              <strong>Reference:</strong> {registrationData.email}
                            </p>
                          </div>
                          <p className="body-small text-blue-700 mt-2">
                            Please use your email address as the payment reference and send proof of payment to
                            <EMAIL>
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Terms and Conditions */}
          <Card className="card-modern">
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="terms"
                  checked={agreedToTerms}
                  onCheckedChange={(checked) => setAgreedToTerms(checked as boolean)}
                  disabled={isProcessing}
                />
                <div className="body-small">
                  <label htmlFor="terms" className="cursor-pointer">
                    I agree to the{" "}
                    <a
                      href="/terms"
                      className="text-green-600 hover:text-green-700 underline"
                      target="_blank"
                      rel="noreferrer"
                    >
                      Terms & Conditions
                    </a>
                    ,{" "}
                    <a
                      href="/privacy"
                      className="text-green-600 hover:text-green-700 underline"
                      target="_blank"
                      rel="noreferrer"
                    >
                      Privacy Policy
                    </a>
                    , and{" "}
                    <a
                      href="/refund-policy"
                      className="text-green-600 hover:text-green-700 underline"
                      target="_blank"
                      rel="noreferrer"
                    >
                      Refund Policy
                    </a>
                  </label>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Payment Summary Sidebar */}
        <div className="lg:col-span-1">
          <div className="sticky top-8">
            <Card className="card-modern">
              <CardHeader>
                <CardTitle className="heading-4 text-green-700 flex items-center">
                  <Shield className="w-5 h-5 mr-2" />
                  Payment Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Registration Details */}
                <div>
                  <h6 className="body-base font-semibold text-gray-900 mb-3">Registration Details</h6>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="body-small text-gray-600">Registration Type</span>
                      <span className="body-small font-semibold text-gray-900">
                        {getUserTypeDisplay(registrationData.type)}
                      </span>
                    </div>
                    {registrationData.type === "exhibitor" && (
                      <div className="flex justify-between">
                        <span className="body-small text-gray-600">Booth Size</span>
                        <span className="body-small font-semibold text-gray-900">
                          {registrationData.boothSize === "premium" ? "Premium (6x6m)" : "Standard (3x3m)"}
                        </span>
                      </div>
                    )}
                    {registrationData.type === "sponsor" && (
                      <div className="flex justify-between">
                        <span className="body-small text-gray-600">Sponsorship Level</span>
                        <span className="body-small font-semibold text-gray-900 capitalize">
                          {registrationData.sponsorshipLevel}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Pricing Breakdown */}
                <div className="border-t border-gray-200 pt-4">
                  <h6 className="body-base font-semibold text-gray-900 mb-3">Pricing Breakdown</h6>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="body-small text-gray-600">Registration Fee</span>
                      <span className="body-small font-semibold text-gray-900">
                        KES {pricing.baseAmount.toLocaleString()}
                      </span>
                    </div>
                    {pricing.processingFee > 0 && (
                      <div className="flex justify-between">
                        <span className="body-small text-gray-600">Processing Fee</span>
                        <span className="body-small font-semibold text-gray-900">
                          KES {pricing.processingFee.toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Total */}
                <div className="border-t border-gray-200 pt-4">
                  <div className="flex justify-between items-center">
                    <span className="heading-4 text-gray-900">Total Amount</span>
                    <span className="heading-3 text-green-700">KES {pricing.total.toLocaleString()}</span>
                  </div>
                  {registrationData.type === "guest" && (
                    <p className="body-small text-green-600 mt-1">Early bird pricing (save KES 5,000)</p>
                  )}
                </div>

                {/* Security Notice */}
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <Shield className="w-5 h-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="body-small font-semibold text-green-900 mb-1">Secure Payment</p>
                      <p className="body-small text-green-700">
                        Your payment information is encrypted and secure. We use industry-standard security measures.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Submit Button */}
                <Button
                  onClick={handleSubmit}
                  disabled={!agreedToTerms || !selectedPaymentMethod || isProcessing}
                  className="btn-success w-full py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isProcessing ? (
                    <div className="flex items-center space-x-3">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Processing Payment...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-3">
                      <CreditCard className="w-5 h-5" />
                      <span>Complete Registration</span>
                    </div>
                  )}
                </Button>

                {/* Payment Methods Notice */}
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="body-small text-gray-600 text-center">
                    We accept all major Kenyan payment methods including M-Pesa, Airtel Money, and bank transfers
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
