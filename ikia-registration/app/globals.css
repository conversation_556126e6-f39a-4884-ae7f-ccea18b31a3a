@tailwind base;
@tailwind components;
@tailwind utilities;

/* IKIA Brand Typography - Acquire & Myriad Pro */
@font-face {
  font-family: "Acquire";
  src: local("Acquire"), local("Acquire-Regular"), local("Arial Black"), local("Impact");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Acquire";
  src: local("Acquire Bold"), local("Acquire-Bold"), local("Arial Black"), local("Impact");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Myriad Pro";
  src: local("Myriad Pro"), local("MyriadPro-Regular"), local("Arial"), local("Helvetica");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Myriad Pro";
  src: local("Myriad Pro Semibold"), local("MyriadPro-Semibold"), local("Arial"), local("Helvetica");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Myriad Pro";
  src: local("Myriad Pro Bold"), local("MyriadPro-Bold"), local("Arial Bold"), local("Helvetica Bold");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* IKIA Brand Design System - Enhanced Color Contrast */
:root {
  /* Primary Brand Colors - High Contrast Usage */
  --ikia-green: #159147;
  --ikia-brown: #7e2518;

  /* Secondary Colors */
  --ikia-ochre: #e8b32c;
  --ikia-blue: #81b1db;
  --ikia-sienna: #c86e36;

  /* Extended Palette for Enhanced Contrast */
  --ikia-green-light: #1ba854;
  --ikia-green-dark: #0f6b35;
  --ikia-green-darker: #0a4d26;
  --ikia-brown-light: #a03020;
  --ikia-brown-dark: #5c1c12;
  --ikia-brown-darker: #3d120c;
  --ikia-ochre-light: #f0c040;
  --ikia-ochre-dark: #d4a025;
  --ikia-blue-light: #9bc5e4;
  --ikia-blue-dark: #6a9bc9;
  --ikia-sienna-light: #d4824a;
  --ikia-sienna-dark: #b05a22;

  /* High Contrast Neutral Scale */
  --ikia-white: #ffffff;
  --ikia-gray-50: #fafafa;
  --ikia-gray-100: #f4f4f5;
  --ikia-gray-200: #e4e4e7;
  --ikia-gray-300: #d4d4d8;
  --ikia-gray-400: #a1a1aa;
  --ikia-gray-500: #71717a;
  --ikia-gray-600: #52525b;
  --ikia-gray-700: #3f3f46;
  --ikia-gray-800: #27272a;
  --ikia-gray-900: #18181b;

  /* Typography */
  --font-heading: "Acquire", "Arial Black", "Impact", system-ui, -apple-system, sans-serif;
  --font-body: "Myriad Pro", "Arial", "Helvetica", system-ui, -apple-system, sans-serif;

  /* Enhanced Shadows with Brand Colors */
  --shadow-green: 0 4px 6px -1px rgba(21, 145, 71, 0.15), 0 2px 4px -1px rgba(21, 145, 71, 0.1);
  --shadow-green-lg: 0 10px 15px -3px rgba(21, 145, 71, 0.15), 0 4px 6px -2px rgba(21, 145, 71, 0.1);
  --shadow-brown: 0 4px 6px -1px rgba(126, 37, 24, 0.15), 0 2px 4px -1px rgba(126, 37, 24, 0.1);
  --shadow-ochre: 0 4px 6px -1px rgba(232, 179, 44, 0.15), 0 2px 4px -1px rgba(232, 179, 44, 0.1);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-body);
  font-size: 1rem;
  line-height: 1.6;
  color: var(--ikia-gray-800);
  background-color: var(--ikia-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Typography System with High Contrast */
.heading-display {
  font-family: var(--font-heading);
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.1;
  letter-spacing: -0.02em;
  color: var(--ikia-gray-900);
}

.heading-1 {
  font-family: var(--font-heading);
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
  color: var(--ikia-gray-900);
}

.heading-2 {
  font-family: var(--font-heading);
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  font-weight: 700;
  line-height: 1.25;
  color: var(--ikia-gray-900);
}

.heading-3 {
  font-family: var(--font-heading);
  font-size: clamp(1.25rem, 2.5vw, 1.875rem);
  font-weight: 600;
  line-height: 1.3;
  color: var(--ikia-gray-800);
}

.heading-4 {
  font-family: var(--font-heading);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.35;
  color: var(--ikia-gray-800);
}

.body-large {
  font-family: var(--font-body);
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--ikia-gray-700);
}

.body-base {
  font-family: var(--font-body);
  font-size: 1rem;
  line-height: 1.6;
  color: var(--ikia-gray-700);
}

.body-small {
  font-family: var(--font-body);
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--ikia-gray-600);
}

.caption {
  font-family: var(--font-body);
  font-size: 0.75rem;
  line-height: 1.4;
  color: var(--ikia-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
}

/* Enhanced Brand Gradient System */
.gradient-primary {
  background: linear-gradient(135deg, var(--ikia-green), var(--ikia-green-light));
}

.gradient-primary-dark {
  background: linear-gradient(135deg, var(--ikia-green-dark), var(--ikia-green));
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--ikia-brown), var(--ikia-brown-light));
}

.gradient-secondary-dark {
  background: linear-gradient(135deg, var(--ikia-brown-dark), var(--ikia-brown));
}

.gradient-accent {
  background: linear-gradient(135deg, var(--ikia-ochre), var(--ikia-ochre-light));
}

.gradient-nature {
  background: linear-gradient(135deg, var(--ikia-green), var(--ikia-blue), var(--ikia-green-light));
}

.gradient-earth {
  background: linear-gradient(135deg, var(--ikia-brown), var(--ikia-sienna), var(--ikia-ochre));
}

.gradient-heritage {
  background: linear-gradient(135deg, var(--ikia-brown), var(--ikia-green), var(--ikia-ochre));
}

/* Enhanced Component Styles with High Contrast */
.btn-primary {
  background: var(--ikia-green);
  color: var(--ikia-white);
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-family: var(--font-body);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-green);
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  background: var(--ikia-green-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-green-lg);
}

.btn-secondary {
  background: var(--ikia-brown);
  color: var(--ikia-white);
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-family: var(--font-body);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-brown);
}

.btn-secondary:hover {
  background: var(--ikia-brown-dark);
  transform: translateY(-2px);
}

.btn-accent {
  background: var(--ikia-ochre);
  color: var(--ikia-white);
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-family: var(--font-body);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-ochre);
}

.btn-accent:hover {
  background: var(--ikia-ochre-dark);
  transform: translateY(-2px);
}

.card-modern {
  background: var(--ikia-white);
  border-radius: 1.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--ikia-gray-200);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.card-modern:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

.card-heritage {
  background: var(--ikia-white);
  border-radius: 1.5rem;
  box-shadow: 0 10px 15px -3px rgba(21, 145, 71, 0.1), 0 4px 6px -2px rgba(21, 145, 71, 0.05);
  border: 2px solid var(--ikia-gray-200);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
}

.card-heritage:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(21, 145, 71, 0.15), 0 10px 10px -5px rgba(21, 145, 71, 0.1);
  border-color: var(--ikia-green);
}

/* Enhanced Grid System for Even Distribution */
.grid-even {
  display: grid;
  gap: 1.5rem;
}

.grid-even-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-even-3 {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-even-4 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-even-5 {
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
}

/* Responsive Grid Adjustments */
@media (max-width: 768px) {
  .grid-even-2,
  .grid-even-3,
  .grid-even-4,
  .grid-even-5 {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-even-3,
  .grid-even-4,
  .grid-even-5 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Enhanced Text Gradient Effects */
.text-gradient-primary {
  background: linear-gradient(135deg, var(--ikia-green), var(--ikia-green-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-secondary {
  background: linear-gradient(135deg, var(--ikia-brown), var(--ikia-brown-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-heritage {
  background: linear-gradient(135deg, var(--ikia-green), var(--ikia-brown), var(--ikia-ochre));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced Animation System */
@keyframes heritage-pulse {
  0%,
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes african-dance {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(5deg);
  }
  50% {
    transform: translateY(-5px) rotate(-5deg);
  }
  75% {
    transform: translateY(-15px) rotate(3deg);
  }
}

.animate-heritage-pulse {
  animation: heritage-pulse 2s ease-in-out infinite;
}

.animate-african-dance {
  animation: african-dance 4s ease-in-out infinite;
}

/* High Contrast Focus Styles */
.focus-modern:focus {
  outline: 3px solid var(--ikia-ochre);
  outline-offset: 2px;
}

/* Enhanced Input Styles */
.input-modern {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--ikia-gray-300);
  border-radius: 0.75rem;
  font-family: var(--font-body);
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--ikia-white);
  color: var(--ikia-gray-800);
}

.input-modern:focus {
  outline: none;
  border-color: var(--ikia-green);
  box-shadow: 0 0 0 3px rgba(21, 145, 71, 0.1);
}

/* Print Styles */
@media print {
  .gradient-primary,
  .gradient-secondary,
  .gradient-accent,
  .gradient-nature,
  .gradient-earth,
  .gradient-heritage {
    background: var(--ikia-gray-800) !important;
    color: var(--ikia-white) !important;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
